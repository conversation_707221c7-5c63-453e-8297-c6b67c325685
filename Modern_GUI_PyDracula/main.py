# ///////////////////////////////////////////////////////////////
#
# BY: WANDERSON M.PIMENTA
# PROJECT MADE WITH: Qt Designer and PySide6
# V: 1.0.0
#
# This project can be used freely for all uses, as long as they maintain the
# respective credits only in the Python scripts, any information in the visual
# interface (GUI) can be modified without any implication.
#
# There are limitations on Qt licenses if you want to use your products
# commercially, I recommend reading them on the official website:
# https://doc.qt.io/qtforpython/licenses.html
#
# ///////////////////////////////////////////////////////////////

import sys
import os
import platform
import asyncio
import logging
import threading
from datetime import datetime

# IMPORT / GUI AND MODULES AND WIDGETS
# ///////////////////////////////////////////////////////////////
from modules import *
from modules.news_analyzer import NewsAnalyzer
from widgets import *
from PySide6.QtCore import Signal, QTimer, Qt
from PySide6.QtWidgets import QHeaderView, QMessageBox, QDialog, QVBoxLayout, QLabel, QTextEdit, QWidget, QHBoxLayout, QPushButton, QLineEdit, QComboBox
# 确保matplotlib使用正确的后端
import matplotlib
matplotlib.use('Qt5Agg')
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from modules.stock_analyzer import StockAnalyzer

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
os.environ["QT_FONT_DPI"] = "96" # FIX Problem for High DPI and Scale above 100%

# SET AS GLOBAL WIDGETS
# ///////////////////////////////////////////////////////////////
widgets = None

class MainWindow(QMainWindow):
    # 定义信号
    news_update_signal = Signal(list)
    startup_analysis_signal = Signal()  # 新增信号用于触发启动分析

    def __init__(self):
        QMainWindow.__init__(self)

        # SET AS GLOBAL WIDGETS
        # ///////////////////////////////////////////////////////////////
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        global widgets
        widgets = self.ui

        # 配置日志
        self.setup_logging()

        # 连接信号
        self.news_update_signal.connect(self.update_news_table)
        self.startup_analysis_signal.connect(self.startup_news_analysis)

        # 初始化新闻分析器
        self.news_analyzer = NewsAnalyzer()
        self.setup_news_table()

        # 添加标志来控制首次新闻爬取
        self.initial_crawl_triggered = False
        # 添加标志来跟踪历史新闻加载状态
        self.news_history_loading = False
        self.news_history_loaded = False

        # 爬取开关控制
        self.enable_jin10_crawl = True      # 是否启用金十数据爬取
        self.enable_cls_crawl = True        # 是否启用财联社数据爬取（重新启用）

        # 严格顺序爬取控制变量（按照约定规则）
        self.crawl_state = {
            # 全局爬取状态
            'is_any_crawling': False,           # 是否有任何数据源正在爬取
            'last_global_crawl_start_time': 0,  # 上次任何爬取的开始时间

            # 金十数据相关
            'jin10_is_crawling': False,         # 金十数据是否正在爬取
            'jin10_last_crawl_start_time': 0,   # 金十数据上次爬取开始时间
            'jin10_crawl_interval': 30,         # 金十数据爬取间隔（秒）
            'jin10_consecutive_count': 0,       # 连续金十数据爬取次数

            # 财联社数据相关
            'cls_is_crawling': False,           # 财联社数据是否正在爬取
            'cls_last_crawl_start_time': 0,     # 财联社数据上次爬取开始时间
            'cls_crawl_interval': 300,          # 财联社数据爬取间隔（秒）

            # 通用控制
            'crawl_timer': None,                # 爬取定时器
            'first_startup_crawl': True         # 是否是启动后的第一次爬取
        }

        # 分页相关变量
        self.current_page = 1
        self.page_size = 15  # 默认页面大小，将动态调整
        self.total_pages = 1
        self.min_page_size = 5  # 最小页面大小
        self.max_page_size = 50  # 最大页面大小
        self.base_row_height = 30  # 基础行高（像素）
        self.min_row_height = 25  # 最小行高
        self.max_row_height = 60  # 最大行高
        self.auto_adjust_page_size = True  # 是否启用自动调整页面大小
        self.auto_adjust_row_height = True  # 是否启用自动调整行高

        self.init_news_analyzer()

        # 注意：定时器将在GLM客户端初始化成功后启动

        # 初始化股票分析器，传入新闻分析器的引用
        self.stock_analyzer = StockAnalyzer(news_analyzer=self.news_analyzer)

        # USE CUSTOM TITLE BAR | USE AS "False" FOR MAC OR LINUX
        # ///////////////////////////////////////////////////////////////
        Settings.ENABLE_CUSTOM_TITLE_BAR = True

        # APP NAME
        # ///////////////////////////////////////////////////////////////
        title = "PyDracula - Modern GUI"
        description = "PyDracula APP - Theme with colors based on Dracula for Python."
        # APPLY TEXTS
        self.setWindowTitle(title)
        widgets.titleRightInfo.setText(description)

        # TOGGLE MENU
        # ///////////////////////////////////////////////////////////////
        widgets.toggleButton.clicked.connect(lambda: UIFunctions.toggleMenu(self, True))

        # SET UI DEFINITIONS
        # ///////////////////////////////////////////////////////////////
        UIFunctions.uiDefinitions(self)

        # # QTableWidget PARAMETERS
        # # ///////////////////////////////////////////////////////////////
        # widgets.tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # BUTTONS CLICK
        # ///////////////////////////////////////////////////////////////

        # LEFT MENUS
        widgets.btn_home.clicked.connect(self.buttonClick)
        widgets.btn_widgets.clicked.connect(self.buttonClick)
        widgets.btn_new.clicked.connect(self.buttonClick)
        widgets.btn_save.clicked.connect(self.buttonClick)

        # EXTRA LEFT BOX
        def openCloseLeftBox():
            UIFunctions.toggleLeftBox(self, True)
        widgets.toggleLeftBox.clicked.connect(openCloseLeftBox)
        widgets.extraCloseColumnBtn.clicked.connect(openCloseLeftBox)

        # EXTRA RIGHT BOX
        def openCloseRightBox():
            UIFunctions.toggleRightBox(self, True)
            # 动态添加输入框控件
            self.add_dynamic_input_to_settings()
        widgets.settingsTopBtn.clicked.connect(openCloseRightBox)

        # SHOW APP
        # ///////////////////////////////////////////////////////////////
        self.show()

        # SET CUSTOM THEME
        # ///////////////////////////////////////////////////////////////
        useCustomTheme = False
        themeFile = "themes\py_dracula_light.qss"

        # SET THEME AND HACKS
        if useCustomTheme:
            # LOAD AND APPLY STYLE
            UIFunctions.theme(self, themeFile, True)

            # SET HACKS
            AppFunctions.setThemeHack(self)

        # SET HOME PAGE AND SELECT MENU
        # ///////////////////////////////////////////////////////////////
        widgets.stackedWidget.setCurrentWidget(widgets.new_page)
        # widgets.btn_home.setStyleSheet(UIFunctions.selectMenu(widgets.btn_home.styleSheet()))
        widgets.btn_widgets.setStyleSheet(UIFunctions.selectMenu(widgets.btn_new.styleSheet()))

        # 延迟检查API Key状态并显示警告（备用机制）
        QTimer.singleShot(5000, self.check_token_and_show_warning)


    # BUTTONS CLICK
    # Post here your functions for clicked buttons
    # ///////////////////////////////////////////////////////////////
    def buttonClick(self):
        # GET BUTTON CLICKED
        btn = self.sender()
        btnName = btn.objectName()

        # # SHOW WIDGETS PAGE
        # if btnName == "btn_widgets":
        #     widgets.stackedWidget.setCurrentWidget(widgets.widgets)
        #     UIFunctions.resetStyle(self, btnName)
        #     btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # # SHOW HOME PAGE
        # if btnName == "btn_home":
        #     widgets.stackedWidget.setCurrentWidget(widgets.home)
        #     UIFunctions.resetStyle(self, btnName)
        #     btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # SHOW NEW PAGE
        if btnName == "btn_new":
            widgets.stackedWidget.setCurrentWidget(widgets.new_page) # SET PAGE
            UIFunctions.resetStyle(self, btnName) # RESET ANOTHERS BUTTONS SELECTED
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet())) # SELECT MENU
        #
        # if btnName == "btn_save":
        #     print("Save BTN clicked!")
        #
        # # PRINT BTN NAME
        # print(f'Button "{btnName}" pressed!')


    # RESIZE EVENTS
    # ///////////////////////////////////////////////////////////////
    def resizeEvent(self, event):
        # Update Size Grips
        UIFunctions.resize_grips(self)

        # 动态调整页面大小（但要避免在历史新闻加载期间触发）
        if hasattr(self, 'news_table') and self.auto_adjust_page_size:
            # 检查是否已经完成初始化
            if hasattr(self, 'news_history_loaded') and self.news_history_loaded:
                self.adjust_page_size_to_window()
            else:
                # 历史新闻还没加载完成，跳过表格更新，避免误触发
                print("📰 历史新闻尚未加载完成，跳过窗口自适应调整")

        # 调用父类的resizeEvent
        super().resizeEvent(event)

    # MOUSE CLICK EVENTS
    # ///////////////////////////////////////////////////////////////
    def mousePressEvent(self, event):
        # SET DRAG POS WINDOW
        self.dragPos = event.globalPos()

        # PRINT MOUSE EVENTS
        if event.buttons() == Qt.LeftButton:
            print('Mouse click: LEFT CLICK')
        if event.buttons() == Qt.RightButton:
            print('Mouse click: RIGHT CLICK')

    # 新闻分析相关方法
    # ///////////////////////////////////////////////////////////////
    def setup_news_table(self):
        """设置新闻表格"""
        # 清空原有的label
        if hasattr(self.ui, 'label'):
            self.ui.label.hide()

        # 创建表格
        self.news_table = QTableWidget()
        self.news_table.setColumnCount(7)
        self.news_table.setHorizontalHeaderLabels(['时间', '股票', '匹配类型', '股票类型', '新闻内容', '市场情绪', '操作'])

        # 设置表格属性
        self.news_table.setAlternatingRowColors(True)
        self.news_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.news_table.setSortingEnabled(True)

        # 设置表格背景为白色
        self.news_table.setStyleSheet("""
            QTableWidget {
                background-color: rgb(255, 255, 255);
                gridline-color: rgb(230, 230, 230);
                border: 1px solid rgb(200, 200, 200);
            }
            QTableWidget::item {
                background-color: rgb(255, 255, 255);
                border: none;
            }
            QTableWidget::item:selected {
                background-color: rgb(0, 0, 0);
                color: rgb(255, 255, 255);
            }
            QHeaderView::section {
                background-color: rgb(245, 245, 245);
                color: rgb(50, 50, 50);
                border: 1px solid rgb(200, 200, 200);
                font-weight: bold;
            }
        """)

        # 设置列头居中对齐
        header = self.news_table.horizontalHeader()
        header.setDefaultAlignment(Qt.AlignCenter)

        # 获取表格总宽度并按比例分配
        total_width = 1000  # 假设总宽度

        # 设置列宽 - 新闻内容占50%，扩大时间和股票列
        self.news_table.setColumnWidth(0, 160)  # 时间 - 扩大
        self.news_table.setColumnWidth(1, 120)  # 股票 - 扩大
        self.news_table.setColumnWidth(2, 80)   # 匹配类型
        self.news_table.setColumnWidth(3, 60)   # 股票类型 - 新增列
        self.news_table.setColumnWidth(4, int(total_width * 0.5))  # 新闻内容 50%
        self.news_table.setColumnWidth(5, 60)   # 市场情绪 - 只容纳两个汉字
        self.news_table.setColumnWidth(6, 80)   # 操作列 - 容纳分析按钮

        # 设置新闻内容列可以拉伸
        self.news_table.horizontalHeader().setStretchLastSection(False)
        self.news_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Stretch)

        # 连接单元格点击事件
        self.news_table.cellClicked.connect(self.on_cell_clicked)

        # 添加到布局
        self.ui.verticalLayout_20.addWidget(self.news_table)

        # 创建分页控件
        self.setup_pagination_controls()

        # 初始化页面大小调整（延迟执行，确保UI完全加载）
        QTimer.singleShot(500, self.initial_page_size_adjustment)

        # 页面加载时自动刷新新闻
        QTimer.singleShot(1000, self.auto_refresh_news)  # 延迟1秒后自动刷新

    def setup_pagination_controls(self):
        """设置分页控件"""
        # 创建分页控件容器
        pagination_widget = QWidget()
        pagination_widget.setStyleSheet("background-color: rgb(255, 255, 255);")  # 设置白色背景
        pagination_layout = QHBoxLayout(pagination_widget)
        pagination_layout.setContentsMargins(10, 5, 10, 5)

        # 添加左侧间距
        pagination_layout.addStretch()

        # 首页按钮
        self.first_page_btn = QPushButton("首页")
        self.first_page_btn.setMaximumWidth(60)
        self.first_page_btn.setStyleSheet("color: rgb(25, 25, 25); font-weight: bold;")
        self.first_page_btn.clicked.connect(self.go_to_first_page)
        pagination_layout.addWidget(self.first_page_btn)

        # 上一页按钮
        self.prev_page_btn = QPushButton("上一页")
        self.prev_page_btn.setMaximumWidth(60)
        self.prev_page_btn.setStyleSheet("color: rgb(25, 25, 25); font-weight: bold;")
        self.prev_page_btn.clicked.connect(self.go_to_prev_page)
        pagination_layout.addWidget(self.prev_page_btn)

        # 页码信息标签
        self.page_info_label = QLabel("第 1 页 / 共 1 页")
        self.page_info_label.setAlignment(Qt.AlignCenter)
        self.page_info_label.setMinimumWidth(120)
        self.page_info_label.setStyleSheet("color: rgb(25, 25, 25); font-weight: bold; font-size: 12px;")
        pagination_layout.addWidget(self.page_info_label)

        # 下一页按钮
        self.next_page_btn = QPushButton("下一页")
        self.next_page_btn.setMaximumWidth(60)
        self.next_page_btn.setStyleSheet("color: rgb(25, 25, 25); font-weight: bold;")
        self.next_page_btn.clicked.connect(self.go_to_next_page)
        pagination_layout.addWidget(self.next_page_btn)

        # 末页按钮
        self.last_page_btn = QPushButton("末页")
        self.last_page_btn.setMaximumWidth(60)
        self.last_page_btn.setStyleSheet("color: rgb(25, 25, 25); font-weight: bold;")
        self.last_page_btn.clicked.connect(self.go_to_last_page)
        pagination_layout.addWidget(self.last_page_btn)

        # 跳转到指定页面
        jump_label = QLabel("跳转到:")
        jump_label.setStyleSheet("color: rgb(25, 25, 25); font-weight: bold;")
        pagination_layout.addWidget(jump_label)

        self.page_input = QLineEdit()
        self.page_input.setMaximumWidth(50)
        self.page_input.setPlaceholderText("页码")
        self.page_input.setStyleSheet("color: rgb(25, 25, 25); background-color: white; border: 1px solid #ccc;")
        self.page_input.returnPressed.connect(self.go_to_page)
        pagination_layout.addWidget(self.page_input)

        go_btn = QPushButton("跳转")
        go_btn.setMaximumWidth(50)
        go_btn.setStyleSheet("color: rgb(25, 25, 25); font-weight: bold;")
        go_btn.clicked.connect(self.go_to_page)
        pagination_layout.addWidget(go_btn)

        # 每页显示数量
        page_size_label = QLabel("每页:")
        page_size_label.setStyleSheet("color: rgb(25, 25, 25); font-weight: bold;")
        pagination_layout.addWidget(page_size_label)

        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["自动", "10", "15", "20", "30", "50"])
        self.page_size_combo.setCurrentText("自动")
        self.page_size_combo.setMaximumWidth(60)
        self.page_size_combo.setStyleSheet("color: rgb(25, 25, 25); background-color: white; border: 1px solid #ccc;")
        self.page_size_combo.currentTextChanged.connect(self.change_page_size)
        pagination_layout.addWidget(self.page_size_combo)

        items_label = QLabel("条")
        items_label.setStyleSheet("color: rgb(25, 25, 25); font-weight: bold;")
        pagination_layout.addWidget(items_label)

        # 添加行高调整选项
        row_height_label = QLabel("行高:")
        row_height_label.setStyleSheet("color: rgb(25, 25, 25); font-weight: bold;")
        pagination_layout.addWidget(row_height_label)

        self.row_height_combo = QComboBox()
        self.row_height_combo.addItems(["自动", "小", "中", "大"])
        self.row_height_combo.setCurrentText("自动")
        self.row_height_combo.setMaximumWidth(60)
        self.row_height_combo.setStyleSheet("color: rgb(25, 25, 25); background-color: white; border: 1px solid #ccc;")
        self.row_height_combo.currentTextChanged.connect(self.change_row_height_mode)
        pagination_layout.addWidget(self.row_height_combo)

        # 添加右侧间距
        pagination_layout.addStretch()

        # 添加到主布局
        self.ui.verticalLayout_20.addWidget(pagination_widget)

    def init_news_analyzer(self):
        """初始化新闻分析器"""
        # 在后台线程中初始化
        import threading
        def init_thread():
            # 优先级1: 首先检查API Key并加载历史新闻数据
            print("🔍 第一优先级: 检查GLM-Z1-Flash API Key...")
            api_key = self.news_analyzer.db.get_token()
            if api_key is None:
                print("❌ 未找到API Key，不加载历史新闻数据")
                print("🔍 DEBUG: 由于API Key不存在，不会更新UI和启动定时器")
                # 弹出警告对话框 - 使用QTimer确保在主线程中执行
                print("🔍 DEBUG: 准备显示API Key警告弹窗...")
                QTimer.singleShot(100, self.show_token_warning)
                # 即使没有API Key，也要尝试加载股票数据（在后台）
                self.load_stock_data_in_background()
                return

            # API Key存在，立即加载历史新闻数据并更新UI
            print("✅ API Key存在，立即加载历史新闻数据...")
            self.news_history_loading = True
            db_news = self.news_analyzer.db.load_all_news()
            self.news_analyzer.news_history.extend(db_news)
            self.news_history_loading = False
            self.news_history_loaded = True

            # 如果有历史数据，立即更新UI（最高优先级）
            if db_news:
                print(f"📰 发现 {len(db_news)} 条历史新闻，立即更新界面")
                QTimer.singleShot(50, lambda: self.update_news_table([]))
                # 历史新闻加载完成后，触发页面大小调整
                QTimer.singleShot(100, self.trigger_delayed_adjustments)
            else:
                print("📰 没有历史新闻数据")
                # 即使没有历史数据，也要触发页面调整
                QTimer.singleShot(100, self.trigger_delayed_adjustments)

            # 优先级2: 在后台加载股票数据（不阻塞UI更新）
            print("🔍 第二优先级: 在后台加载股票数据...")
            self.load_stock_data_in_background()

            # 优先级3: 初始化GLM客户端
            print("🔍 第三优先级: 初始化GLM客户端...")
            glm_success = self.news_analyzer.init_chatbot()
            print(f"🔍 DEBUG: GLM客户端初始化结果: {glm_success}")
            if glm_success:
                print("✅ 新闻分析器初始化完成")
                print("🔍 启动定时新闻分析...")

                # 启动定时器（不立即执行第一次分析）
                try:
                    import threading
                    print("🔍 使用Python threading定时器（5分钟间隔）")
                    # 只启动定时器，不立即执行第一次分析
                    self.setup_threading_timer()
                    print("✅ 定时器启动成功，等待历史记录UI更新完毕后开始第一次分析")

                except Exception as e:
                    print(f"❌ 定时器启动失败: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                print("❌ GLM-Z1-Flash客户端初始化失败")
                print("🔍 DEBUG: 由于GLM客户端初始化失败，不会启动定时器")
                # 弹出警告对话框
                QTimer.singleShot(2000, self.show_token_warning)

        thread = threading.Thread(target=init_thread)
        thread.daemon = True
        thread.start()

    def load_stock_data_in_background(self):
        """在后台线程中加载股票数据，不阻塞UI"""
        import threading
        def stock_load_thread():
            try:
                print("📊 开始在后台加载股票数据...")
                success = self.news_analyzer.load_stock_data()
                if success:
                    print("✅ 股票数据加载完成")

                    # 获取并显示当前股票信息（只在有股票信息时显示）
                    try:
                        current_stock_info = self.news_analyzer.get_current_stock_display_info()
                        if current_stock_info['stock_code']:  # 只有当有股票代码时才显示
                            print(f"📊 当前股票信息: {current_stock_info['display_text']} ({current_stock_info['stock_code']})")
                            # 在主线程中更新UI显示当前股票信息
                            QTimer.singleShot(50, lambda: self.update_current_stock_display(current_stock_info))
                        else:
                            print("📊 当前无选择股票")

                    except Exception as e:
                        print(f"❌ 获取当前股票信息失败: {e}")

                    # 股票数据加载完成后，刷新表格以显示正确的股票名称
                    if hasattr(self, 'news_analyzer') and self.news_analyzer.news_history:
                        print("📊 股票数据加载完成，刷新表格显示股票名称")
                        QTimer.singleShot(100, lambda: self.refresh_current_page())
                    else:
                        # 如果没有历史新闻且股票数据已加载完成，触发首次新闻爬取
                        print("📊 股票数据加载完成，没有历史新闻，准备启动首次新闻爬取...")
                        print(f"🔍 DEBUG: initial_crawl_triggered = {self.initial_crawl_triggered}")
                        if not self.initial_crawl_triggered:
                            print("🔍 DEBUG: 设置 initial_crawl_triggered = True")
                            self.initial_crawl_triggered = True
                            print("🔍 DEBUG: 使用线程安全信号触发 startup_news_analysis")
                            # 使用线程安全的信号发射
                            self.startup_analysis_signal.emit()
                        else:
                            print("🔍 DEBUG: initial_crawl_triggered 已经为 True，跳过触发")
                else:
                    print("❌ 股票数据加载失败")
            except Exception as e:
                print(f"❌ 后台加载股票数据时出错: {e}")
                import traceback
                traceback.print_exc()

        thread = threading.Thread(target=stock_load_thread)
        thread.daemon = True
        thread.start()

    def update_current_stock_display(self, stock_info):
        """更新界面上的当前股票信息显示"""
        try:
            # 只有当有股票代码时才更新界面显示
            if not stock_info['stock_code']:
                print("📊 无股票信息，跳过界面更新")
                return

            # 更新窗口标题显示当前股票
            current_title = self.windowTitle()
            if " - " in current_title:
                base_title = current_title.split(" - ")[0]
            else:
                base_title = current_title

            new_title = f"{base_title} - 当前股票: {stock_info['display_text']}"
            self.setWindowTitle(new_title)
            print(f"✅ 窗口标题已更新: {new_title}")

            # 如果有状态栏，也可以在状态栏显示
            if hasattr(self, 'statusBar') and self.statusBar():
                self.statusBar().showMessage(f"当前关注股票: {stock_info['display_text']} ({stock_info['stock_code']})")
                print(f"✅ 状态栏已更新: 当前关注股票: {stock_info['display_text']}")

        except Exception as e:
            print(f"❌ 更新当前股票显示失败: {e}")

    def trigger_delayed_adjustments(self):
        """在历史新闻加载完成后触发延迟的UI调整"""
        try:
            print("📰 历史新闻加载完成，执行延迟的UI调整")
            if self.auto_adjust_page_size:
                # 获取表格的实际行高
                self.update_row_height()
                # 调整页面大小
                self.adjust_page_size_to_window()
        except Exception as e:
            print(f"延迟UI调整失败: {e}")

    def auto_refresh_news(self):
        """页面加载时自动刷新新闻"""
        print("=== 页面加载，自动刷新新闻 ===")

        # 首先检查API Key是否存在
        api_key = self.news_analyzer.db.get_token()
        if api_key is None:
            print("❌ API Key不存在，不更新table UI数据")
            return

        # 检查历史新闻是否已经加载并显示
        if self.news_analyzer.news_history:
            print("发现历史新闻，直接更新界面")
            self.update_news_table([])  # 传入空列表，只显示历史数据
        else:
            print("📰 历史新闻尚未加载，跳过自动刷新（避免重复触发）")
            # 不触发新闻爬取，因为初始化流程会处理这个情况
            return

        # 只有在有历史数据的情况下才进行新闻爬取
        # 这样可以避免与初始化流程的冲突
        print("📰 历史数据已显示，开始新闻爬取...")
        self.refresh_news_internal()

    def refresh_news_internal(self):
        """内部刷新新闻方法"""
        print("=== 开始刷新新闻 ===")

        # 首先检查API Key是否存在
        api_key = self.news_analyzer.db.get_token()
        if api_key is None:
            print("❌ API Key不存在，不进行新闻爬取")
            return

        # 在后台线程中执行新闻分析
        import threading
        def analyze_thread():
            try:
                print("开始新闻分析线程")

                # 定义进度回调函数
                def progress_callback(news_item):
                    """每处理完一条新闻就更新UI"""
                    print(f"进度回调：新增新闻 {news_item['stock']}")
                    # 使用信号在主线程中更新UI
                    self.news_update_signal.emit([news_item])

                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                print("开始分析新闻...")
                analyzed_news = loop.run_until_complete(
                    self.news_analyzer.analyze_news(progress_callback=progress_callback)
                )
                loop.close()
                print(f"新闻分析完成，总共处理了 {len(analyzed_news)} 条新闻")

                # 最后再发送一次完整更新信号（以防有遗漏）
                if analyzed_news:
                    print("发送最终更新信号...")
                    self.news_update_signal.emit([])  # 空列表表示刷新全部
            except Exception as e:
                print(f"新闻分析出错: {e}")
                import traceback
                traceback.print_exc()

        thread = threading.Thread(target=analyze_thread)
        thread.daemon = True
        thread.start()

    def update_news_table(self, news_list):
        """更新新闻表格"""
        print(f"=== 开始更新表格，传入新闻数量: {len(news_list)} ===")
        try:
            # 如果传入的是单条新闻（增量更新）
            if len(news_list) == 1:
                print("执行增量更新")
                # 增量更新时，直接刷新整个表格以保持分页正确性
                self.refresh_current_page()
                return

            # 否则执行完整刷新
            print("执行完整刷新")
            # 获取所有历史新闻
            all_news = self.news_analyzer.get_news_history()
            print(f"历史新闻总数: {len(all_news)}")

            # 按时间倒序排列（最新的在前面）
            all_news_sorted = sorted(all_news, key=lambda x: x['timestamp'], reverse=True)
            print("新闻已按时间倒序排列")

            # 更新分页信息
            self.update_pagination_info()

            # 获取当前页的数据
            page_data = self.get_page_data(all_news_sorted)
            print(f"当前第{self.current_page}页，显示{len(page_data)}条记录")

            # 设置表格行数为当前页的数据量
            self.news_table.setRowCount(len(page_data))
            print(f"设置表格行数: {len(page_data)}")

            # 应用当前的行高设置
            if hasattr(self, 'base_row_height') and self.auto_adjust_row_height:
                self.apply_row_height_to_table(self.base_row_height)

            # 填充当前页数据
            for row, news_item in enumerate(page_data):
                print(f"处理第 {row+1} 行新闻: {news_item.get('stock', 'Unknown')}")

                # 根据市场情绪确定文字颜色
                if news_item['sentiment'] == '正面':
                    text_color = QColor(139, 0, 0)      # 深红色
                elif news_item['sentiment'] == '负面':
                    text_color = QColor(0, 100, 0)      # 深绿色
                elif news_item['sentiment'] == '中性':
                    text_color = QColor(75, 0, 130)     # 深紫色
                elif news_item['sentiment'] == '未知':
                    text_color = QColor(105, 105, 105)  # 深灰色
                else:
                    text_color = QColor(105, 105, 105)  # 深灰色（默认）

                # 创建居中对齐的表格项，使用display_time显示
                display_time = news_item.get('display_time', news_item.get('timestamp', ''))
                timestamp_item = self.create_table_item_with_font(display_time, 0, text_color)
                self.news_table.setItem(row, 0, timestamp_item)

                # 股票列只显示股票名称，不显示代码
                stock_display = self.get_stock_name_only(news_item['stock'], news_item['match_type'])
                stock_item = self.create_table_item_with_font(stock_display, 1, text_color)
                self.news_table.setItem(row, 1, stock_item)

                match_type_text = "股票代码" if news_item['match_type'] == 'code' else "股票名称"
                match_type_item = QTableWidgetItem(match_type_text)
                match_type_item.setTextAlignment(Qt.AlignCenter)
                match_type_item.setForeground(text_color)
                self.news_table.setItem(row, 2, match_type_item)

                # 股票类型列
                stock_type = news_item.get('stock_type', 'A')
                stock_type_item = self.create_table_item_with_font(stock_type, 3, text_color)
                self.news_table.setItem(row, 3, stock_type_item)

                # 新闻内容居中对齐 - 添加数据源标识
                source = news_item.get('source', '财联社')
                content_with_source = f"[{source}] {news_item['content']}"
                content_item = self.create_table_item_with_font(content_with_source, 4, text_color)
                self.news_table.setItem(row, 4, content_item)

                sentiment_item = self.create_table_item_with_font(news_item['sentiment'], 5, text_color)
                self.news_table.setItem(row, 5, sentiment_item)

                # 根据情绪设置整行背景颜色（颜色对调）
                if news_item['sentiment'] == '正面':
                    row_color = QColor(255, 182, 193)  # 淡红色
                elif news_item['sentiment'] == '负面':
                    row_color = QColor(144, 238, 144)  # 淡绿色
                else:
                    row_color = QColor(255, 255, 255)  # 白色

                # 设置整行背景颜色
                for col in range(6):  # 前6列设置背景色（增加了股票类型列）
                    item = self.news_table.item(row, col)
                    if item:
                        item.setBackground(row_color)

                # 创建操作按钮容器
                button_widget = QWidget()
                button_layout = QHBoxLayout(button_widget)
                button_layout.setContentsMargins(2, 2, 2, 2)
                button_layout.setSpacing(5)

                # 添加分析按钮
                analyze_button = QPushButton("分析")
                analyze_button.setMaximumWidth(50)
                analyze_button.setMaximumHeight(25)
                analyze_button.setStyleSheet("""
                    QPushButton {
                        background-color: rgb(0, 128, 0);
                        color: rgb(0, 0, 0);
                        border: none;
                        border-radius: 3px;
                        font-size: 10px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: rgb(0, 150, 0);
                        color: rgb(0, 0, 0);
                    }
                    QPushButton:pressed {
                        background-color: rgb(0, 100, 0);
                        color: rgb(0, 0, 0);
                    }
                """)
                # 使用新闻的唯一标识（时间戳+股票）来识别新闻
                news_id = f"{news_item['timestamp']}_{news_item['stock']}"
                analyze_button.clicked.connect(lambda checked, nid=news_id: self.analyze_news_by_id(nid))
                button_layout.addWidget(analyze_button)

                # 添加删除按钮
                delete_button = QPushButton("删除")
                delete_button.setMaximumWidth(50)
                delete_button.setMaximumHeight(25)
                delete_button.setStyleSheet("""
                    QPushButton {
                        background-color: rgb(220, 53, 69);
                        color: rgb(25, 25, 25);
                        border: none;
                        border-radius: 3px;
                        font-size: 10px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: rgb(200, 33, 49);
                        color: rgb(25, 25, 25);
                    }
                    QPushButton:pressed {
                        background-color: rgb(180, 13, 29);
                        color: rgb(25, 25, 25);
                    }
                """)
                delete_button.clicked.connect(lambda checked, nid=news_id: self.delete_news_by_id(nid))
                button_layout.addWidget(delete_button)

                self.news_table.setCellWidget(row, 6, button_widget)

            # 滚动到最新的新闻
            if all_news:
                self.news_table.scrollToBottom()
                print("表格更新完成，滚动到底部")

                # 更新行高并重新调整页面大小（如果启用了自动调整）
                if self.auto_adjust_page_size and len(page_data) > 0:
                    # 延迟执行，确保表格渲染完成
                    QTimer.singleShot(100, self.update_row_height_and_adjust)

                # 如果这是历史记录的首次加载，触发第一次新闻爬取
                if not self.initial_crawl_triggered:
                    print("✅ 历史记录UI更新完毕，立即开始第一次新闻爬取")
                    self.initial_crawl_triggered = True
                    # 使用线程安全的信号确保在主线程中执行
                    self.startup_analysis_signal.emit()
            else:
                print("没有新闻数据")
                # 检查是否正在加载历史新闻（避免在加载期间误触发）
                if hasattr(self, 'news_history_loading') and self.news_history_loading:
                    print("📰 历史新闻正在加载中，跳过触发新闻爬取")
                    return

                # 检查是否已经加载过历史新闻
                if hasattr(self, 'news_history_loaded') and self.news_history_loaded:
                    print("📰 历史新闻已加载完成，但当前页面无数据")
                    return

                # 只有在确实没有API Key或者初始化完成后才触发第一次爬取
                api_key = self.news_analyzer.db.get_token()
                if api_key is None:
                    print("📰 没有API Key，跳过新闻爬取")
                elif not self.initial_crawl_triggered:
                    print("✅ 没有历史数据，立即开始第一次新闻爬取")
                    self.initial_crawl_triggered = True
                    self.startup_analysis_signal.emit()
                else:
                    print("📰 初始化已完成，跳过重复触发")

        except Exception as e:
            print(f"更新表格出错: {e}")
            import traceback
            traceback.print_exc()

    def add_single_news_to_table(self, news_item):
        """将单条新闻添加到表格顶部（增量更新）"""
        print(f"=== 增量添加新闻: {news_item.get('stock', 'Unknown')} ===")
        try:
            # 在表格顶部插入一行
            self.news_table.insertRow(0)
            print("在表格顶部插入新行")

            # 根据市场情绪确定文字颜色
            if news_item['sentiment'] == '正面':
                text_color = QColor(139, 0, 0)      # 深红色
            elif news_item['sentiment'] == '负面':
                text_color = QColor(0, 100, 0)      # 深绿色
            elif news_item['sentiment'] == '中性':
                text_color = QColor(75, 0, 130)     # 深紫色
            elif news_item['sentiment'] == '未知':
                text_color = QColor(105, 105, 105)  # 深灰色
            else:
                text_color = QColor(105, 105, 105)  # 深灰色（默认）

            # 创建居中对齐的表格项，使用display_time显示
            display_time = news_item.get('display_time', news_item.get('timestamp', ''))
            timestamp_item = self.create_table_item_with_font(display_time, 0, text_color)
            self.news_table.setItem(0, 0, timestamp_item)

            # 股票列只显示股票名称，不显示代码
            stock_display = self.get_stock_name_only(news_item['stock'], news_item['match_type'])
            stock_item = self.create_table_item_with_font(stock_display, 1, text_color)
            self.news_table.setItem(0, 1, stock_item)

            match_type_text = "股票代码" if news_item['match_type'] == 'code' else "股票名称"
            match_type_item = QTableWidgetItem(match_type_text)
            match_type_item.setTextAlignment(Qt.AlignCenter)
            match_type_item.setForeground(text_color)
            self.news_table.setItem(0, 2, match_type_item)

            # 新闻内容居中对齐 - 使用带字体设置的函数
            content_item = self.create_table_item_with_font(news_item['content'], 3, text_color)
            self.news_table.setItem(0, 3, content_item)

            sentiment_item = self.create_table_item_with_font(news_item['sentiment'], 4, text_color)
            self.news_table.setItem(0, 4, sentiment_item)

            # 根据情绪设置整行背景颜色（颜色对调）
            if news_item['sentiment'] == '正面':
                row_color = QColor(255, 182, 193)  # 淡红色
            elif news_item['sentiment'] == '负面':
                row_color = QColor(144, 238, 144)  # 淡绿色
            else:
                row_color = QColor(255, 255, 255)  # 白色

            # 设置整行背景颜色
            for col in range(5):  # 前5列设置背景色
                item = self.news_table.item(0, col)
                if item:
                    item.setBackground(row_color)

            # 创建操作按钮容器
            button_widget = QWidget()
            button_layout = QHBoxLayout(button_widget)
            button_layout.setContentsMargins(2, 2, 2, 2)
            button_layout.setSpacing(5)

            # 添加分析按钮
            analyze_button = QPushButton("分析")
            analyze_button.setMaximumWidth(50)
            analyze_button.setMaximumHeight(25)
            analyze_button.setStyleSheet("""
                QPushButton {
                    background-color: rgb(0, 128, 0);
                    color: rgb(0, 0, 0);
                    border: none;
                    border-radius: 3px;
                    font-size: 10px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: rgb(0, 150, 0);
                    color: rgb(0, 0, 0);
                }
                QPushButton:pressed {
                    background-color: rgb(0, 100, 0);
                    color: rgb(0, 0, 0);
                }
            """)
            # 使用新闻的唯一标识（时间戳+股票）来识别新闻
            news_id = f"{news_item['timestamp']}_{news_item['stock']}"
            analyze_button.clicked.connect(lambda checked, nid=news_id: self.analyze_news_by_id(nid))
            button_layout.addWidget(analyze_button)

            # 添加删除按钮
            delete_button = QPushButton("删除")
            delete_button.setMaximumWidth(50)
            delete_button.setMaximumHeight(25)
            delete_button.setStyleSheet("""
                QPushButton {
                    background-color: rgb(220, 53, 69);
                    color: rgb(25, 25, 25);
                    border: none;
                    border-radius: 3px;
                    font-size: 10px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: rgb(200, 33, 49);
                    color: rgb(25, 25, 25);
                }
                QPushButton:pressed {
                    background-color: rgb(180, 13, 29);
                    color: rgb(25, 25, 25);
                }
            """)
            delete_button.clicked.connect(lambda checked, nid=news_id: self.delete_news_by_id(nid))
            button_layout.addWidget(delete_button)

            self.news_table.setCellWidget(0, 5, button_widget)

            # 滚动到顶部显示新添加的新闻
            self.news_table.scrollToTop()
            print(f"新闻已添加到表格顶部: {news_item.get('stock', 'Unknown')}")

            # 强制刷新界面
            self.news_table.repaint()
            QApplication.processEvents()

        except Exception as e:
            print(f"增量添加新闻出错: {e}")
            import traceback
            traceback.print_exc()

    def analyze_news_by_id(self, news_id):
        """根据新闻ID分析新闻详情"""
        try:
            print(f"分析新闻ID: {news_id}")
            # 解析新闻ID
            timestamp, stock = news_id.rsplit('_', 1)

            # 在历史新闻中查找匹配的新闻
            all_news = self.news_analyzer.get_news_history()
            target_news = None

            for news_item in all_news:
                if news_item['timestamp'] == timestamp and news_item['stock'] == stock:
                    target_news = news_item
                    break

            if target_news:
                print(f"找到目标新闻: {target_news['stock']}")
                self.show_news_content_dialog(target_news)
            else:
                print(f"未找到新闻ID对应的新闻: {news_id}")

        except Exception as e:
            print(f"分析新闻详情出错: {e}")
            import traceback
            traceback.print_exc()

    def on_cell_clicked(self, row, column):
        """处理单元格点击事件"""
        try:
            # 只处理新闻内容列（第4列）的点击
            if column == 4:
                # 获取排序后的新闻列表，与表格显示顺序一致
                all_news = self.news_analyzer.get_news_history()
                all_news_sorted = sorted(all_news, key=lambda x: x['timestamp'], reverse=True)

                # 获取当前页的数据
                page_data = self.get_page_data(all_news_sorted)

                if row < len(page_data):
                    news_item = page_data[row]  # 使用当前页的数据
                    print(f"点击新闻内容 - 第{self.current_page}页第{row+1}行: {news_item['stock']}")

                    # 只显示新闻内容，不显示K线图和筹码分布图
                    self.show_simple_news_dialog(news_item)
        except Exception as e:
            print(f"处理单元格点击出错: {e}")

    def show_simple_news_dialog(self, news_item):
        """显示简单的新闻内容对话框（只显示新闻内容）"""
        try:
            print(f"=== 显示简单新闻对话框: {news_item['stock']} ===")

            # 创建简单对话框
            dialog = QDialog()
            dialog.setWindowTitle("新闻内容详情")
            dialog.setModal(True)
            dialog.resize(800, 600)

            # 设置窗口标志，支持最大化、最小化和关闭按钮
            dialog.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

            # 创建主布局
            main_layout = QVBoxLayout(dialog)

            # 创建新闻信息标签
            source = news_item.get('source', '财联社')
            sequence_info = f" (序号: {news_item['sequence_num']})" if news_item.get('sequence_num') else ""
            info_text = (
                f"数据源: {source}{sequence_info}\n"
                f"股票: {news_item['stock']}\n"
                f"时间: {news_item.get('display_time', news_item.get('timestamp', ''))}\n"
                f"情绪: {news_item['sentiment']}\n"
            )
            info_label = QLabel(info_text)
            info_label.setStyleSheet("font-size: 14px; font-weight: bold; padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
            main_layout.addWidget(info_label)

            # 创建新闻内容文本框
            content_text = QTextEdit()
            content_text.setPlainText(news_item['content'])
            content_text.setReadOnly(True)
            content_text.setStyleSheet("font-size: 36px; padding: 10px; border: 1px solid #ccc; border-radius: 5px;")
            main_layout.addWidget(content_text)

            # 创建关闭按钮
            button_layout = QHBoxLayout()
            button_layout.addStretch()

            close_button = QPushButton("关闭")
            close_button.setStyleSheet("QPushButton { padding: 8px 20px; font-size: 12px; }")
            close_button.clicked.connect(dialog.close)
            button_layout.addWidget(close_button)

            main_layout.addLayout(button_layout)

            # 显示对话框
            dialog.exec_()
            print("简单新闻对话框已关闭")

        except Exception as e:
            print(f"显示简单新闻对话框出错: {e}")
            import traceback
            traceback.print_exc()

    def show_news_content_dialog(self, news_item):
        """显示新闻内容详情对话框 - 完全照抄debug_dialog.py的成功实现"""
        try:
            print(f"=== 开始显示新闻详情对话框: {news_item['stock']} ===")

            # 创建自定义对话框 - 支持最大化
            dialog = QDialog()  # 注意：不传self参数，和debug_dialog.py一样
            dialog.setWindowTitle("新闻内容详情 - 筹码分布分析")
            dialog.setModal(True)
            dialog.resize(1400, 800)

            # 设置窗口标志，支持最大化、最小化和关闭按钮
            dialog.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

            # 创建主布局
            main_layout = QHBoxLayout(dialog)

            # 左侧：新闻内容区域
            left_widget = QWidget()
            left_layout = QVBoxLayout(left_widget)
            # 移除固定宽度限制，使用比例分配
            left_widget.setMinimumWidth(400)  # 设置最小宽度而不是最大宽度

            # 添加标题信息
            info_text = f"""股票: {news_item['stock']} ({news_item['match_type']})
时间: {news_item['timestamp']}
情绪: {news_item['sentiment']}"""

            info_label = QLabel(info_text)
            info_label.setStyleSheet("font-weight: bold; padding: 10px; background-color: #f0f0f0;")
            left_layout.addWidget(info_label)

            # 添加新闻内容 - 占一半高度
            content_text = QTextEdit()
            content_text.setPlainText(news_item['content'])
            content_text.setReadOnly(True)
            content_text.setStyleSheet("padding: 10px; font-size: 24px; line-height: 1.5;")
            left_layout.addWidget(content_text, 1)  # 设置拉伸因子为1

            # 添加筹码统计信息显示区域 - 占一半高度
            stats_label = QLabel("筹码分布统计信息")
            stats_label.setStyleSheet("font-weight: bold; font-size: 12px; padding: 5px; background-color: #e0e0e0;")
            stats_label.setMaximumHeight(30)  # 限制标签高度
            left_layout.addWidget(stats_label)

            stats_text = QTextEdit()
            stats_text.setPlainText("正在计算筹码分布统计信息...")
            stats_text.setReadOnly(True)
            stats_text.setStyleSheet("padding: 8px; font-size: 22px; background-color: #f9f9f9; border: 1px solid #ddd;")
            left_layout.addWidget(stats_text, 1)  # 设置拉伸因子为1，与新闻内容等高

            # 添加关闭按钮
            close_button = QPushButton("关闭")
            close_button.clicked.connect(dialog.accept)
            close_button.setStyleSheet("padding: 8px 20px; font-size: 12px;")
            left_layout.addWidget(close_button)

            main_layout.addWidget(left_widget, 1)  # 左侧占1份
            print("✅ 左侧新闻内容区域创建完成")

            # 右侧：筹码分布图区域
            right_widget = QWidget()
            right_layout = QVBoxLayout(right_widget)

            main_layout.addWidget(right_widget, 2)  # 右侧占2份，图表区域更大
            print("✅ 右侧图表区域创建完成")

            # 创建筹码分布图 - 完全按照debug_dialog.py的方式
            print("=== 开始创建筹码分布图 ===")

            try:
                # 获取股票名称
                stock_name = self.get_stock_name_only(news_item['stock'], news_item['match_type'])
                print(f"处理股票名称: {stock_name}")

                # 使用CYQ算法进行分析，同时获取统计信息
                chart, message = self.stock_analyzer.analyze_stock_cyq(stock_name)

                # 检查是否是流通股本获取失败，直接弹警告窗口
                if chart is None and ("流通股本" in message or "获取股票数据失败" in message):
                    QMessageBox.warning(self, "获取数据失败", "获取流通股本数据失败，无法继续分析")
                    return

                if chart is not None:
                    # 获取筹码统计信息
                    try:
                        # 重新进行CYQ分析以获取统计数据
                        code, market = self.stock_analyzer.get_stock_code_info(stock_name)
                        if code is not None:
                            stock_data = self.stock_analyzer.get_stock_data(code, market, days=0)  # 获取所有历史数据
                            if stock_data is not None:
                                cyq_analysis = self.stock_analyzer.cyq_analyzer.analyze_stock_with_cyq(stock_data)
                                if cyq_analysis is not None:
                                    # 格式化统计信息
                                    stats_info = self.stock_analyzer.cyq_analyzer._format_chip_statistics(cyq_analysis)
                                    stats_text.setPlainText(stats_info)
                                    print("✅ 筹码统计信息已更新到左侧")
                                else:
                                    stats_text.setPlainText("筹码分布计算失败")
                            else:
                                stats_text.setPlainText("股票数据获取失败")
                        else:
                            stats_text.setPlainText("股票代码解析失败")
                    except Exception as stats_e:
                        print(f"获取筹码统计信息失败: {stats_e}")
                        stats_text.setPlainText(f"统计信息获取失败: {str(stats_e)}")

                    # 创建FigureCanvas - 自适应大小
                    print("=== 创建FigureCanvas ===")
                    canvas = FigureCanvas(chart)
                    canvas.setMinimumSize(600, 400)  # 设置最小尺寸
                    # 移除最大尺寸限制，允许自适应窗口大小

                    # 添加到布局，设置拉伸因子使其填满可用空间
                    right_layout.addWidget(canvas, 1)
                    print("✅ FigureCanvas已添加到布局")

                    # 强制刷新画布
                    canvas.draw()
                    print("✅ FigureCanvas已刷新")
                    print("✅ 筹码分布图表已成功添加到对话框")
                else:
                    # 这种情况不应该发生，因为流通股本失败已经在上面处理了
                    raise Exception(f"CYQ分析失败: {message}")

            except Exception as chart_e:
                print(f"❌ 创建筹码分布图失败: {chart_e}")
                import traceback
                traceback.print_exc()

                # 更新统计信息显示错误
                stats_text.setPlainText(f"筹码分布分析失败:\n{str(chart_e)}")

                # 显示错误信息
                error_label = QLabel(f"创建筹码分布图失败:\n{str(chart_e)}")
                error_label.setStyleSheet("color: red; font-size: 12px; padding: 20px;")
                error_label.setAlignment(Qt.AlignCenter)
                right_layout.addWidget(error_label)

            # 显示对话框 - 完全按照debug_dialog.py
            print("=== 显示对话框 ===")
            dialog.exec()

        except Exception as e:
            print(f"❌ 显示新闻详情对话框出错: {e}")
            import traceback
            traceback.print_exc()
            # 备用简单对话框
            QMessageBox.information(self, "新闻内容",
                f"股票: {news_item['stock']}\n"
                f"时间: {news_item['timestamp']}\n"
                f"情绪: {news_item['sentiment']}\n\n"
                f"内容:\n{news_item['content']}")

    def analyze_news_detail(self, row):
        """分析按钮点击处理"""
        try:
            # 获取排序后的新闻列表，与表格显示顺序一致
            all_news = self.news_analyzer.get_news_history()
            all_news_sorted = sorted(all_news, key=lambda x: x['timestamp'], reverse=True)

            if row < len(all_news_sorted):
                news_item = all_news_sorted[row]  # 使用排序后的列表
                stock_name = news_item['stock']
                print(f"点击分析按钮 - 第{row+1}行: {stock_name}")

                # 显示股票分析窗口
                self.show_stock_analysis_dialog(stock_name)

        except Exception as e:
            print(f"分析详情出错: {e}")
            QMessageBox.warning(self, "错误", f"分析出错: {e}")

    def show_stock_analysis_dialog(self, stock_name):
        """显示股票分析对话框 - 同步版本"""
        try:
            print(f"=== 开始显示股票分析对话框: {stock_name} ===")

            # 使用 CYQ 进行股票分析（同步）
            print(f"开始同步分析股票: {stock_name}")

            # 使用 CYQ 分析
            chart, message = self.stock_analyzer.analyze_stock_cyq(stock_name)
            analysis_method = "CYQ"

            print(f"同步分析完成，使用方法: {analysis_method}, 图表对象: {chart is not None}, 消息: {message}")

            if chart is None:
                print(f"❌ 股票分析失败: {message}")
                # 检查是否是流通股本获取失败，直接弹警告窗口
                if "流通股本" in message or "获取股票数据失败" in message:
                    QMessageBox.warning(self, "获取数据失败", "获取流通股本数据失败，无法继续分析")
                else:
                    QMessageBox.warning(self, "分析失败", f"股票分析失败:\n{message}")
                return

            # 创建对话框
            dialog = QDialog()  # 不传self参数，像test_chip_distribution.py一样
            dialog.setWindowTitle(f"筹码分布分析 - {stock_name}")
            dialog.setModal(True)
            dialog.resize(1400, 800)

            # 设置窗口标志，支持最大化、最小化和关闭按钮
            dialog.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

            # 创建主布局
            main_layout = QHBoxLayout(dialog)

            # 左侧：股票信息区域
            left_widget = QWidget()
            left_layout = QVBoxLayout(left_widget)
            left_widget.setMinimumWidth(300)  # 设置最小宽度而不是最大宽度

            # 添加股票信息
            info_text = f"筹码分布分析 - {stock_name}"
            info_label = QLabel(info_text)
            info_label.setStyleSheet("font-weight: bold; font-size: 16px; padding: 10px; background-color: #f0f0f0;")
            info_label.setAlignment(Qt.AlignCenter)
            left_layout.addWidget(info_label)

            # 添加分析结果信息
            result_text = f"""分析结果:

✅ 股票代码解析成功
✅ 股票数据获取成功
✅ 筹码分布计算完成
✅ 分析图表生成成功

分析方法: {analysis_method}
状态: {message}

说明:
• 红色区域: 获利筹码
• 绿色区域: 套牢筹码
• 蓝色虚线: 主力成本
• 黑色实线: 当前价格"""

            result_label = QTextEdit()
            result_label.setPlainText(result_text)
            result_label.setReadOnly(True)
            result_label.setStyleSheet("padding: 10px; font-size: 11px; line-height: 1.4; background-color: #f9f9f9;")
            left_layout.addWidget(result_label)

            # 添加关闭按钮
            close_button = QPushButton("关闭")
            close_button.clicked.connect(dialog.accept)
            close_button.setStyleSheet("padding: 8px 20px; font-size: 12px;")
            left_layout.addWidget(close_button)

            main_layout.addWidget(left_widget, 1)  # 左侧占1份
            print("✅ 左侧信息区域创建完成")

            # 右侧：筹码分布图区域
            right_widget = QWidget()
            right_layout = QVBoxLayout(right_widget)

            # 添加图表标题
            chart_title = QLabel("筹码分布图")
            chart_title.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px; text-align: center;")
            chart_title.setAlignment(Qt.AlignCenter)
            right_layout.addWidget(chart_title)

            # 创建FigureCanvas并添加图表
            print("=== 创建FigureCanvas ===")
            canvas = FigureCanvas(chart)
            canvas.setMinimumSize(600, 400)  # 设置最小尺寸
            # 移除最大尺寸限制，允许自适应窗口大小
            right_layout.addWidget(canvas, 1)  # 设置拉伸因子使其填满可用空间
            print("✅ FigureCanvas已添加到布局")

            # 强制刷新画布
            canvas.draw()
            print("✅ FigureCanvas已刷新")

            main_layout.addWidget(right_widget, 2)  # 右侧占2份，图表区域更大
            print("✅ 右侧图表区域创建完成")

            # 显示对话框（阻塞）
            print("=== 显示对话框 ===")
            dialog.exec()

        except Exception as e:
            print(f"❌ 显示股票分析对话框出错: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self, "错误", f"显示分析窗口出错: {e}")



    def create_chip_distribution_chart_like_debug(self, stock_name):
        """创建筹码分布图 - 使用CYQ算法"""
        print(f"=== 创建筹码分布图: {stock_name} ===")

        try:
            # 使用CYQ算法进行分析
            chart, message = self.stock_analyzer.analyze_stock_cyq(stock_name)

            if chart is not None:
                print(f"✅ 使用CYQ算法成功创建图表: {message}")
                return chart
            else:
                print(f"❌ CYQ算法失败: {message}")
                # 检查是否是流通股本获取失败
                if "流通股本" in message or "获取股票数据失败" in message:
                    raise Exception("获取流通股本数据失败，无法继续分析")
                else:
                    raise Exception(f"CYQ分析失败: {message}")

        except Exception as e:
            print(f"❌ 创建筹码分布图失败: {e}")
            # 创建错误图表
            from matplotlib.figure import Figure
            fig = Figure(figsize=(10, 6), dpi=100)
            ax = fig.add_subplot(111)
            ax.text(0.5, 0.5, f'筹码分布图创建失败\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center', fontsize=12)
            ax.set_title(f"筹码分布图 - {stock_name} (错误)", fontsize=14)
            return fig



    def get_stock_name_only(self, stock_info, match_type):
        """获取股票名称，如果是代码则转换为名称"""
        try:
            # 检查股票数据是否已加载
            if not hasattr(self.news_analyzer, 'stock_codes') or not self.news_analyzer.stock_codes:
                print(f"📊 股票数据尚未加载，暂时显示原始信息: {stock_info}")
                return stock_info

            if match_type == 'code':
                # 如果匹配的是股票代码，需要找到对应的股票名称
                stock_codes = self.news_analyzer.stock_codes
                stock_names = self.news_analyzer.stock_names

                if stock_codes and stock_names and stock_info in stock_codes:
                    index = stock_codes.index(stock_info)
                    if index < len(stock_names):
                        return stock_names[index]
                    else:
                        print(f"⚠️ 股票代码 {stock_info} 的索引超出股票名称列表范围")
                        return stock_info
                else:
                    print(f"⚠️ 股票代码 {stock_info} 未在股票列表中找到")
                    return stock_info  # 如果找不到对应名称，返回原始信息
            else:
                # 如果匹配的是股票名称，直接返回
                return stock_info
        except Exception as e:
            print(f"获取股票名称出错: {e}")
            return stock_info

    def clear_news_history(self):
        """清空新闻历史"""
        self.news_analyzer.clear_history()
        self.news_table.setRowCount(0)
        print("新闻历史已清空")

    def setup_logging(self):
        """配置日志系统"""
        try:
            # 创建logs目录
            log_dir = "logs"
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)

            # 配置日志格式
            log_format = '%(asctime)s - %(levelname)s - %(message)s'

            # 生成日志文件名（按日期）
            log_filename = os.path.join(log_dir, f"news_analyzer_{datetime.now().strftime('%Y%m%d')}.log")

            # 配置日志
            logging.basicConfig(
                level=logging.INFO,
                format=log_format,
                handlers=[
                    logging.FileHandler(log_filename, encoding='utf-8'),
                    logging.StreamHandler(sys.stdout)  # 同时输出到控制台
                ]
            )

            logging.info("=== 新闻分析系统启动 ===")
            logging.info(f"日志文件: {log_filename}")
            print(f"日志配置完成，日志文件: {log_filename}")

        except Exception as e:
            print(f"日志配置失败: {e}")



    # 旧的定时分析方法已被严格顺序爬取逻辑替代



    def add_dynamic_input_to_settings(self):
        """动态添加输入框控件到设置面板"""
        try:
            # 检查是否已经添加过输入框，避免重复添加
            if hasattr(self, 'dynamic_input_added') and self.dynamic_input_added:
                # 如果已经添加过，更新输入框中的API Key显示
                try:
                    current_api_key = self.news_analyzer.db.get_token()
                    if current_api_key and hasattr(self, 'dynamic_input'):
                        self.dynamic_input.setText(current_api_key)
                        print(f"更新输入框显示当前API Key: {current_api_key[:20]}...")
                except Exception as e:
                    print(f"更新API Key显示失败: {e}")
                return

            print("=== 动态添加输入框到设置面板 ===")

            # 创建输入框容器
            input_container = QWidget()
            input_container.setObjectName("dynamic_input_container")
            input_layout = QVBoxLayout(input_container)
            input_layout.setSpacing(10)
            input_layout.setContentsMargins(15, 15, 15, 15)

            # 添加标题标签
            title_label = QLabel("GLM-Z1-Flash API Key设置")
            title_label.setObjectName("dynamic_title_label")
            title_label.setStyleSheet("""
                QLabel {
                    color: rgb(255, 255, 255);
                    font-size: 14px;
                    font-weight: bold;
                    padding: 5px;
                }
            """)
            input_layout.addWidget(title_label)

            # 创建输入框
            self.dynamic_input = QLineEdit()
            self.dynamic_input.setObjectName("dynamic_input_field")
            self.dynamic_input.setPlaceholderText("请输入GLM-Z1-Flash API Key...")
            self.dynamic_input.setMinimumHeight(35)

            # 尝试从数据库加载当前API Key并显示
            try:
                current_api_key = self.news_analyzer.db.get_token()
                if current_api_key:
                    self.dynamic_input.setText(current_api_key)
            except Exception as e:
                print(f"加载当前API Key失败: {e}")
            self.dynamic_input.setStyleSheet("""
                QLineEdit {
                    background-color: rgb(33, 37, 43);
                    border: 2px solid rgb(33, 37, 43);
                    border-radius: 5px;
                    padding: 5px;
                    color: rgb(255, 255, 255);
                    font-size: 12px;
                }
                QLineEdit:focus {
                    border: 2px solid rgb(189, 147, 249);
                }
            """)
            input_layout.addWidget(self.dynamic_input)

            # 创建确认按钮
            confirm_button = QPushButton("保存")
            confirm_button.setObjectName("dynamic_confirm_button")
            confirm_button.setMinimumHeight(35)
            confirm_button.setStyleSheet("""
                QPushButton {
                    background-color: rgb(189, 147, 249);
                    border: none;
                    border-radius: 5px;
                    padding: 5px;
                    color: rgb(255, 255, 255);
                    font-size: 12px;
                    font-weight: bold;
                    text-align: center;
                }
                QPushButton:hover {
                    background-color: rgb(169, 127, 229);
                }
                QPushButton:pressed {
                    background-color: rgb(149, 107, 209);
                }
            """)
            confirm_button.clicked.connect(self.on_dynamic_input_confirm)
            input_layout.addWidget(confirm_button)

            # 添加到设置面板的布局中
            widgets.verticalLayout_13.addWidget(input_container)

            # 标记已添加
            self.dynamic_input_added = True
            print("✅ 动态输入框已添加到设置面板")

        except Exception as e:
            print(f"❌ 添加动态输入框失败: {e}")
            import traceback
            traceback.print_exc()

    def on_dynamic_input_confirm(self):
        """处理动态输入框的保存事件"""
        try:
            api_key = self.dynamic_input.text().strip()
            if api_key:
                print(f"用户输入API Key: {api_key[:20]}...")  # 只显示前20个字符

                # 保存API Key到数据库
                if self.news_analyzer.db.save_token(api_key):
                    # 尝试使用新API Key初始化GLM客户端
                    if self.news_analyzer.init_chatbot(api_key):
                        QMessageBox.information(self, "保存成功", "API Key已保存并成功初始化GLM-Z1-Flash客户端\n\n程序将开始加载历史数据并启动新闻分析")

                        # API Key保存成功后，开始执行后续逻辑
                        self.start_post_token_operations()

                        # 保持API Key显示在输入框中，不清空
                    else:
                        QMessageBox.warning(self, "API Key无效", "API Key已保存，但初始化GLM-Z1-Flash客户端失败，请检查API Key是否正确")
                else:
                    QMessageBox.critical(self, "保存失败", "API Key保存到数据库失败，请重试")
            else:
                QMessageBox.warning(self, "输入为空", "请输入API Key后再保存")
        except Exception as e:
            print(f"❌ 处理API Key保存失败: {e}")
            QMessageBox.critical(self, "保存异常", f"保存API Key时发生异常:\n{str(e)}")

    def start_post_token_operations(self):
        """API Key保存成功后执行的操作"""
        try:
            print("=== 开始执行API Key保存后的操作 ===")

            # 在后台线程中执行，避免阻塞UI
            import threading
            def post_token_thread():
                try:
                    # 1. 加载历史新闻数据（如果还没有加载）
                    print("📚 加载历史新闻数据...")
                    if not self.news_analyzer.news_history:  # 只有当历史数据为空时才加载
                        db_news = self.news_analyzer.db.load_all_news()
                        self.news_analyzer.news_history.extend(db_news)
                    else:
                        db_news = self.news_analyzer.news_history  # 使用已有的历史数据

                    # 2. 如果有历史数据，更新UI
                    if db_news:
                        print(f"✅ 发现 {len(db_news)} 条历史新闻，更新界面")
                        # 使用QTimer在主线程中更新UI
                        QTimer.singleShot(100, lambda: self.update_news_table([]))
                    else:
                        # 没有历史数据时，也要确保触发第一次爬取
                        print("📰 没有历史数据，准备立即执行启动新闻爬取...")
                        if not self.initial_crawl_triggered:
                            self.initial_crawl_triggered = True
                            # 使用线程安全的信号触发启动分析
                            self.startup_analysis_signal.emit()

                    # 3. 启动定时器（不立即执行第一次分析）
                    print("⏰ 启动定时新闻分析...")
                    try:
                        import threading
                        print("🔍 使用Python threading定时器（5分钟间隔）")
                        self.setup_threading_timer()
                        print("✅ 定时器启动成功，等待历史记录UI更新完毕后开始第一次分析")
                    except Exception as e:
                        print(f"❌ 定时器启动失败: {e}")

                except Exception as e:
                    print(f"❌ API Key保存后操作失败: {e}")
                    import traceback
                    traceback.print_exc()

            thread = threading.Thread(target=post_token_thread)
            thread.daemon = True
            thread.start()

        except Exception as e:
            print(f"❌ 启动API Key保存后操作失败: {e}")

    def check_token_and_show_warning(self):
        """检查API Key状态并显示警告（备用机制）"""
        try:
            print("🔍 DEBUG: 执行备用API Key检查...")
            api_key = self.news_analyzer.db.get_token()
            if api_key is None:
                print("🔍 DEBUG: 备用检查发现API Key不存在，显示警告弹窗")
                self.show_token_warning()
            else:
                print("🔍 DEBUG: 备用检查发现API Key存在，无需显示警告")
        except Exception as e:
            print(f"❌ 备用API Key检查失败: {e}")

    def show_token_warning(self):
        """显示API Key缺失警告"""
        try:
            print("🔍 DEBUG: show_token_warning 方法被调用")
            msg = QMessageBox(self)  # 设置父窗口
            msg.setIcon(QMessageBox.Warning)
            msg.setWindowTitle("请输入API Key")
            msg.setText("未找到GLM-Z1-Flash API Key")
            msg.setInformativeText("请点击右上角设置按钮输入API Key，再进行操作。\n\n没有API Key将无法：\n• 加载历史新闻数据\n• 进行新闻情绪分析\n• 自动更新新闻")
            msg.setStandardButtons(QMessageBox.Ok)

            # 确保弹窗在最前面
            msg.setWindowFlags(msg.windowFlags() | Qt.WindowStaysOnTopHint)
            msg.raise_()
            msg.activateWindow()

            print("🔍 DEBUG: 准备显示警告弹窗...")
            result = msg.exec()
            print(f"🔍 DEBUG: 警告弹窗已关闭，返回值: {result}")
        except Exception as e:
            print(f"❌ 显示API Key警告失败: {e}")
            import traceback
            traceback.print_exc()



    def start_threading_timer(self):
        """启动基于Python threading的定时器（已废弃，直接调用setup_threading_timer）"""
        try:
            print("🔧 启动Python threading定时器（5分钟间隔）")
            # 不再立即执行启动爬取，等待UI更新完毕后触发
            print("📊 等待历史记录UI更新完毕后执行程序启动爬取")

            # 启动定时器
            self.setup_threading_timer()

        except Exception as e:
            print(f"❌ start_threading_timer 失败: {e}")
            import traceback
            traceback.print_exc()

    def startup_news_analysis(self):
        """程序启动时执行的新闻分析（不计入定时分析计数）"""
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            logging.info(f"=== 程序启动新闻分析开始 - {current_time} ===")
            print(f"🚀 程序启动新闻分析被触发 - {current_time}")
            print("📊 这是程序启动时的初始新闻爬取（不计入定时分析）")

            # 添加更多调试信息
            print(f"🔍 DEBUG: startup_news_analysis 函数开始执行")
            print(f"🔍 DEBUG: news_analyzer 对象存在: {hasattr(self, 'news_analyzer')}")
            if hasattr(self, 'news_analyzer'):
                print(f"🔍 DEBUG: GLM客户端状态: {hasattr(self.news_analyzer, 'glm_client') and self.news_analyzer.glm_client is not None}")

            # 在新线程中执行新闻分析，避免阻塞UI
            def analysis_thread():
                try:
                    print(f"🔍 DEBUG: analysis_thread 开始执行")

                    # 定义进度回调函数
                    def progress_callback(news_item):
                        """每处理完一条新闻就更新UI"""
                        logging.info(f"启动分析：新增新闻 {news_item['stock']}")
                        print(f"启动分析：新增新闻 {news_item['stock']}")
                        # 使用信号在主线程中更新UI
                        self.news_update_signal.emit([news_item])

                    print(f"🔍 DEBUG: 准备创建事件循环")
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    print(f"🔍 DEBUG: 事件循环创建成功，开始调用 analyze_news")

                    # 启动时根据开关决定爬取哪些数据源
                    jin10_news = []
                    cls_news = []

                    if self.enable_jin10_crawl:
                        print("📰 启动分析：开始爬取金十数据...")
                        jin10_news = loop.run_until_complete(
                            self.news_analyzer.analyze_news(progress_callback=progress_callback, source="jin10")
                        )

                    if self.enable_cls_crawl:
                        print("📰 启动分析：开始爬取财联社数据...")
                        cls_news = loop.run_until_complete(
                            self.news_analyzer.analyze_news(progress_callback=progress_callback, source="cls")
                        )

                    loop.close()
                    print(f"🔍 DEBUG: analyze_news 执行完成")

                    total_news = len(cls_news) + len(jin10_news)
                    logging.info(f"启动分析完成，新增 {total_news} 条新闻（金十: {len(jin10_news)}, 财联社: {len(cls_news)}）")
                    print(f"启动分析完成，新增 {total_news} 条新闻（金十: {len(jin10_news)}, 财联社: {len(cls_news)}）")

                    # 启动分析完成后，初始化爬取状态
                    import time
                    current_time = time.time()
                    if self.enable_jin10_crawl:
                        self.crawl_state['jin10_last_crawl_start_time'] = current_time
                    if self.enable_cls_crawl:
                        self.crawl_state['cls_last_crawl_start_time'] = current_time
                    self.crawl_state['first_startup_crawl'] = False  # 启动爬取已完成

                except Exception as e:
                    logging.error(f"启动新闻分析出错: {e}")
                    print(f"启动新闻分析出错: {e}")
                    import traceback
                    traceback.print_exc()

            # 启动分析线程
            print(f"🔍 DEBUG: 准备启动分析线程")
            thread = threading.Thread(target=analysis_thread)
            thread.daemon = True
            thread.start()
            print(f"🔍 DEBUG: 分析线程已启动")

        except Exception as e:
            logging.error(f"启动新闻分析失败: {e}")
            print(f"启动新闻分析失败: {e}")
            import traceback
            traceback.print_exc()

    def setup_threading_timer(self):
        """设置严格顺序的爬取定时器"""
        try:
            print("✅ 启动严格顺序爬取定时器")
            print("   - 启动后先爬取金十数据，再爬取财联社数据")
            print("   - 金十数据：每1分钟检查一次，满足条件才爬取")
            print("   - 财联社数据：连续5次金十数据后才爬取")

            # 启动定时检查器（每10秒检查一次是否可以爬取）
            self.start_crawl_scheduler()

        except Exception as e:
            print(f"🔍 DEBUG: setup_threading_timer 失败: {e}")
            import traceback
            traceback.print_exc()

    def start_crawl_scheduler(self):
        """启动爬取调度器"""
        try:
            import threading

            def scheduler_callback():
                try:
                    self.check_and_schedule_crawl()
                    # 递归设置下一次检查（每10秒检查一次）
                    self.start_crawl_scheduler()
                except Exception as e:
                    print(f"🔍 DEBUG: scheduler_callback 失败: {e}")
                    import traceback
                    traceback.print_exc()
                    # 即使出错也要继续调度
                    self.start_crawl_scheduler()

            # 设置10秒后执行检查
            timer = threading.Timer(10.0, scheduler_callback)
            timer.daemon = True
            timer.start()

            # 保存定时器引用
            self.crawl_state['crawl_timer'] = timer

        except Exception as e:
            print(f"🔍 DEBUG: start_crawl_scheduler 失败: {e}")
            import traceback
            traceback.print_exc()

    def check_and_schedule_crawl(self):
        """检查并调度爬取任务（按照约定规则）"""
        try:
            import time
            current_time = time.time()

            # 启动后的第一次爬取：只爬取金十数据
            if self.crawl_state['first_startup_crawl']:
                print("🚀 启动后第一次爬取：开始金十数据")
                self.crawl_state['first_startup_crawl'] = False
                if self.enable_jin10_crawl:
                    self.execute_crawl('jin10')
                return

            # 如果有任何数据源正在爬取，跳过检查
            if self.crawl_state['is_any_crawling']:
                print("⏳ 有数据源正在爬取中，跳过本次检查")
                return

            # 检查金十数据爬取条件
            if self.enable_jin10_crawl:
                time_since_last_global = current_time - self.crawl_state['last_global_crawl_start_time']

                # 金十数据爬取条件：
                # 1. 上一次任何数据爬取并分析完毕（通过is_any_crawling=False保证）
                # 2. 距离上一次爬取开始时间 >= 30秒
                if time_since_last_global >= self.crawl_state['jin10_crawl_interval']:
                    print(f"✅ 金十数据爬取条件满足：距离上次全局爬取 {time_since_last_global:.1f} 秒")
                    self.execute_crawl('jin10')
                    return  # 执行金十数据后立即返回
                else:
                    remaining_time = self.crawl_state['jin10_crawl_interval'] - time_since_last_global
                    print(f"⏰ 金十数据等待中：还需等待 {remaining_time:.1f} 秒")

            # 检查财联社数据爬取条件
            if self.enable_cls_crawl:
                # 财联社爬取条件：
                # 1. 前面有连续5次金十数据的爬取和分析
                # 2. 上一次金十数据已经爬取并分析完毕（通过is_any_crawling=False保证）
                if self.crawl_state['jin10_consecutive_count'] >= 5:
                    print(f"✅ 财联社爬取条件满足：已连续爬取 {self.crawl_state['jin10_consecutive_count']} 次金十数据")
                    self.execute_crawl('cls')
                    return  # 执行财联社后立即返回
                else:
                    needed_count = 5 - self.crawl_state['jin10_consecutive_count']
                    print(f"⏰ 财联社等待中：还需 {needed_count} 次连续金十数据爬取")

        except Exception as e:
            print(f"🔍 DEBUG: check_and_schedule_crawl 失败: {e}")
            import traceback
            traceback.print_exc()

    def execute_crawl(self, crawl_type):
        """执行爬取任务（按照约定规则）"""
        try:
            import time
            import threading

            # 检查对应数据源的开关
            if crawl_type == 'jin10' and not self.enable_jin10_crawl:
                print("⚠️ 金十数据爬取已禁用，跳过")
                return
            elif crawl_type == 'cls' and not self.enable_cls_crawl:
                print("⚠️ 财联社数据爬取已禁用，跳过")
                return

            # 检查是否已在爬取中
            if self.crawl_state['is_any_crawling']:
                print("⏳ 已有数据源正在爬取中，跳过")
                return

            # 设置全局和对应的爬取状态
            current_time = time.time()
            self.crawl_state['is_any_crawling'] = True
            self.crawl_state['last_global_crawl_start_time'] = current_time

            if crawl_type == 'jin10':
                self.crawl_state['jin10_is_crawling'] = True
                self.crawl_state['jin10_last_crawl_start_time'] = current_time
            else:
                self.crawl_state['cls_is_crawling'] = True
                self.crawl_state['cls_last_crawl_start_time'] = current_time

            print(f"🚀 开始执行 {'金十数据' if crawl_type == 'jin10' else '财联社'} 爬取")

            def crawl_thread():
                try:
                    if crawl_type == 'jin10':
                        self.execute_jin10_crawl()
                        # 金十数据爬取完成后，增加连续计数
                        self.crawl_state['jin10_consecutive_count'] += 1
                        print(f"📊 连续金十数据爬取次数: {self.crawl_state['jin10_consecutive_count']}")
                    else:
                        self.execute_cls_crawl()
                        # 财联社数据爬取完成后，重置金十数据连续计数
                        self.crawl_state['jin10_consecutive_count'] = 0
                        print("🔄 重置金十数据连续计数，下次将从金十数据开始新的循环")

                except Exception as e:
                    print(f"❌ {crawl_type} 爬取执行失败: {e}")
                    import traceback
                    traceback.print_exc()
                finally:
                    # 无论成功失败都要重置爬取状态
                    self.crawl_state['is_any_crawling'] = False
                    if crawl_type == 'jin10':
                        self.crawl_state['jin10_is_crawling'] = False
                    else:
                        self.crawl_state['cls_is_crawling'] = False
                    print(f"✅ {crawl_type} 爬取任务完成")

            # 在新线程中执行爬取
            thread = threading.Thread(target=crawl_thread)
            thread.daemon = True
            thread.start()

        except Exception as e:
            print(f"🔍 DEBUG: execute_crawl 失败: {e}")
            # 重置爬取状态
            self.crawl_state['is_any_crawling'] = False
            if crawl_type == 'jin10':
                self.crawl_state['jin10_is_crawling'] = False
            else:
                self.crawl_state['cls_is_crawling'] = False

    def execute_jin10_crawl(self):
        """执行金十数据爬取"""
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"📰 开始金十数据爬取 - {current_time}")

            # 定义进度回调函数
            def progress_callback(news_item):
                """每处理完一条新闻就更新UI"""
                print(f"金十数据分析：新增新闻 {news_item['stock']}")
                # 使用信号在主线程中更新UI
                self.news_update_signal.emit([news_item])

            # 执行金十数据分析
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            jin10_news = loop.run_until_complete(
                self.news_analyzer.analyze_news(progress_callback=progress_callback, source="jin10")
            )

            loop.close()

            print(f"✅ 金十数据爬取完成，新增 {len(jin10_news)} 条新闻")

        except Exception as e:
            print(f"❌ 金十数据爬取失败: {e}")
            import traceback
            traceback.print_exc()

    def execute_cls_crawl(self):
        """执行财联社数据爬取"""
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"📰 开始财联社数据爬取 - {current_time}")

            # 定义进度回调函数
            def progress_callback(news_item):
                """每处理完一条新闻就更新UI"""
                print(f"财联社分析：新增新闻 {news_item['stock']}")
                # 使用信号在主线程中更新UI
                self.news_update_signal.emit([news_item])

            # 执行财联社数据分析
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            cls_news = loop.run_until_complete(
                self.news_analyzer.analyze_news(progress_callback=progress_callback, source="cls")
            )

            loop.close()

            print(f"✅ 财联社数据爬取完成，新增 {len(cls_news)} 条新闻")

        except Exception as e:
            print(f"❌ 财联社数据爬取失败: {e}")
            import traceback
            traceback.print_exc()

    def set_jin10_crawl_enabled(self, enabled: bool):
        """设置金十数据爬取开关"""
        self.enable_jin10_crawl = enabled
        status = "启用" if enabled else "禁用"
        print(f"🔧 金十数据爬取已{status}")

    def set_cls_crawl_enabled(self, enabled: bool):
        """设置财联社数据爬取开关"""
        self.enable_cls_crawl = enabled
        status = "启用" if enabled else "禁用"
        print(f"🔧 财联社数据爬取已{status}")

    def get_crawl_status(self):
        """获取当前爬取状态"""
        import time
        current_time = time.time()

        # 计算下次爬取时间
        time_since_last_global = current_time - self.crawl_state['last_global_crawl_start_time']
        next_crawl_in = max(0, self.crawl_state['jin10_crawl_interval'] - time_since_last_global)

        status = {
            'jin10_enabled': self.enable_jin10_crawl,
            'cls_enabled': self.enable_cls_crawl,
            'is_any_crawling': self.crawl_state['is_any_crawling'],
            'jin10_is_crawling': self.crawl_state['jin10_is_crawling'],
            'cls_is_crawling': self.crawl_state['cls_is_crawling'],
            'jin10_consecutive_count': self.crawl_state['jin10_consecutive_count'],
            'jin10_last_crawl': self.crawl_state['jin10_last_crawl_start_time'],
            'cls_last_crawl': self.crawl_state['cls_last_crawl_start_time'],
            'last_global_crawl': self.crawl_state['last_global_crawl_start_time'],
            'next_crawl_in': next_crawl_in
        }

        return status

    def print_crawl_status(self):
        """打印当前爬取状态"""
        status = self.get_crawl_status()

        print("\n" + "="*80)
        print("📊 当前爬取状态（严格顺序规则）:")
        print(f"   全局状态: {'🔄有爬取中' if status.get('is_any_crawling', False) else '⏸️全部空闲'}")
        print(f"   金十数据: {'✅启用' if status['jin10_enabled'] else '❌禁用'} | "
              f"{'🔄爬取中' if status['jin10_is_crawling'] else '⏸️空闲'} | "
              f"连续次数: {status.get('jin10_consecutive_count', 0)}")
        print(f"   财联社: {'✅启用' if status['cls_enabled'] else '❌禁用'} | "
              f"{'🔄爬取中' if status['cls_is_crawling'] else '⏸️空闲'} | "
              f"需要: {max(0, 5 - status.get('jin10_consecutive_count', 0))} 次金十数据")
        print(f"   下次爬取: {status.get('next_crawl_in', 0):.0f}秒后")
        print("="*80)



    def delete_news_by_id(self, news_id: str):
        """根据新闻ID删除新闻记录"""
        try:
            print(f"准备删除新闻: {news_id}")

            # 弹出确认对话框
            reply = QMessageBox.question(
                self,
                "确认删除",
                "确定要删除这条新闻记录吗？\n\n此操作不可撤销。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 解析news_id获取时间戳和股票信息
                parts = news_id.split('_', 1)
                if len(parts) == 2:
                    timestamp, stock = parts

                    # 从数据库删除
                    success = self.news_analyzer.db.delete_news_by_timestamp_and_stock(timestamp, stock)

                    if success:
                        # 从内存中的新闻历史删除
                        self.news_analyzer.news_history = [
                            news for news in self.news_analyzer.news_history
                            if not (news['timestamp'] == timestamp and news['stock'] == stock)
                        ]

                        # 刷新表格显示
                        self.update_news_table([])

                        QMessageBox.information(self, "删除成功", "新闻记录已成功删除")
                        print(f"✅ 新闻删除成功: {news_id}")
                    else:
                        QMessageBox.critical(self, "删除失败", "从数据库删除新闻记录失败")
                        print(f"❌ 数据库删除失败: {news_id}")
                else:
                    QMessageBox.critical(self, "删除失败", "新闻ID格式错误")
                    print(f"❌ 新闻ID格式错误: {news_id}")
            else:
                print("用户取消删除操作")

        except Exception as e:
            print(f"❌ 删除新闻失败: {e}")
            QMessageBox.critical(self, "删除异常", f"删除新闻时发生异常:\n{str(e)}")

    # 分页控制方法
    # ///////////////////////////////////////////////////////////////
    def calculate_total_pages(self, total_items):
        """计算总页数"""
        return max(1, (total_items + self.page_size - 1) // self.page_size)

    def adjust_page_size_to_window(self):
        """根据窗口大小动态调整页面大小和行高"""
        try:
            if not hasattr(self, 'news_table') or not self.news_table:
                return

            # 获取表格容器的可用高度
            available_height = self.get_table_available_height()
            if available_height <= 0:
                return

            # 计算可以显示的行数和行高
            # 减去表头高度（约30像素）和分页控件高度（约40像素）
            usable_height = available_height - 70

            # 策略1：优先保持合理的行数，然后调整行高
            target_rows = 15  # 目标行数
            calculated_row_height = usable_height / target_rows

            # 限制行高在合理范围内
            if calculated_row_height < self.min_row_height:
                # 行高太小，减少行数
                calculated_row_height = self.min_row_height
                calculated_rows = max(self.min_page_size, int(usable_height / calculated_row_height))
            elif calculated_row_height > self.max_row_height:
                # 行高太大，增加行数
                calculated_row_height = self.max_row_height
                calculated_rows = min(self.max_page_size, int(usable_height / calculated_row_height))
            else:
                # 行高合适，使用目标行数
                calculated_rows = target_rows

            # 确保行数在合理范围内
            calculated_rows = max(self.min_page_size, min(self.max_page_size, calculated_rows))

            # 重新计算精确的行高
            final_row_height = int(usable_height / calculated_rows)
            final_row_height = max(self.min_row_height, min(self.max_row_height, final_row_height))

            # 检查是否需要更新
            need_update = False
            if calculated_rows != self.page_size:
                old_page_size = self.page_size
                self.page_size = calculated_rows
                need_update = True
                print(f"页面大小调整：{old_page_size} -> {calculated_rows} 行")

            if self.auto_adjust_row_height and abs(final_row_height - self.base_row_height) > 2:
                old_row_height = self.base_row_height
                self.base_row_height = final_row_height
                need_update = True
                print(f"行高调整：{old_row_height}px -> {final_row_height}px")

                # 应用新的行高到表格
                self.apply_row_height_to_table(final_row_height)

            if need_update:
                print(f"窗口自适应：可用高度={available_height}px, 行数={calculated_rows}, 行高={final_row_height}px")

                # 更新页面大小下拉框的显示（如果存在）
                if hasattr(self, 'page_size_combo'):
                    # 临时断开信号连接，避免触发change_page_size
                    self.page_size_combo.blockSignals(True)
                    # 如果计算出的大小不在预设选项中，添加它
                    if str(calculated_rows) not in [self.page_size_combo.itemText(i) for i in range(self.page_size_combo.count())]:
                        self.page_size_combo.addItem(str(calculated_rows))
                    self.page_size_combo.setCurrentText(str(calculated_rows))
                    self.page_size_combo.blockSignals(False)

                # 调整当前页码，确保不超出范围
                total_news = len(self.news_analyzer.get_news_history()) if hasattr(self, 'news_analyzer') else 0
                if total_news > 0:
                    new_total_pages = self.calculate_total_pages(total_news)
                    if self.current_page > new_total_pages:
                        self.current_page = max(1, new_total_pages)

                # 刷新当前页面显示
                self.refresh_current_page()

        except Exception as e:
            print(f"动态调整页面大小失败: {e}")
            import traceback
            traceback.print_exc()

    def get_table_available_height(self):
        """获取表格容器的可用高度"""
        try:
            if not hasattr(self, 'news_table') or not self.news_table:
                return 0

            # 获取表格所在的容器（new_page）的高度
            if hasattr(self.ui, 'new_page'):
                container_height = self.ui.new_page.height()
                print(f"容器高度: {container_height}px")
                return container_height
            else:
                # 备用方案：使用主窗口高度减去估算的其他组件高度
                window_height = self.height()
                # 减去标题栏、菜单栏、状态栏等的估算高度
                estimated_other_height = 150
                available_height = window_height - estimated_other_height
                print(f"备用计算：窗口高度={window_height}px, 可用高度={available_height}px")
                return max(0, available_height)

        except Exception as e:
            print(f"获取表格可用高度失败: {e}")
            return 0

    def apply_row_height_to_table(self, row_height):
        """将新的行高应用到表格中"""
        try:
            if not hasattr(self, 'news_table') or not self.news_table:
                return

            # 设置表格的默认行高
            self.news_table.verticalHeader().setDefaultSectionSize(row_height)

            # 为现有的所有行设置新的行高
            for row in range(self.news_table.rowCount()):
                self.news_table.setRowHeight(row, row_height)

            print(f"已将表格行高设置为: {row_height}px")

        except Exception as e:
            print(f"应用行高到表格失败: {e}")

    def initial_page_size_adjustment(self):
        """初始页面大小调整"""
        try:
            print("=== 执行初始页面大小调整 ===")
            if self.auto_adjust_page_size:
                # 检查历史新闻是否已加载
                if hasattr(self, 'news_history_loaded') and self.news_history_loaded:
                    # 获取表格的实际行高
                    self.update_row_height()
                    # 调整页面大小
                    self.adjust_page_size_to_window()
                else:
                    print("📰 历史新闻尚未加载，跳过初始页面大小调整")
        except Exception as e:
            print(f"初始页面大小调整失败: {e}")

    def update_row_height(self):
        """更新表格行高的实际值"""
        try:
            if hasattr(self, 'news_table') and self.news_table.rowCount() > 0:
                # 获取第一行的实际高度
                actual_height = self.news_table.rowHeight(0)
                if actual_height > 0:
                    self.base_row_height = actual_height
                    print(f"检测到实际行高: {self.base_row_height}px")
                else:
                    # 如果无法获取实际高度，使用默认值
                    self.base_row_height = 30
                    print(f"使用默认行高: {self.base_row_height}px")
        except Exception as e:
            print(f"更新行高失败: {e}")
            self.base_row_height = 30

    def update_row_height_and_adjust(self):
        """更新行高并重新调整页面大小"""
        try:
            old_row_height = self.base_row_height
            self.update_row_height()

            # 如果行高发生了变化，重新调整页面大小
            if old_row_height != self.base_row_height:
                print(f"行高变化：{old_row_height}px -> {self.base_row_height}px，重新调整页面大小")
                self.adjust_page_size_to_window()
        except Exception as e:
            print(f"更新行高并调整页面大小失败: {e}")

    def change_row_height_mode(self, mode):
        """改变行高模式"""
        try:
            if mode == "自动":
                self.auto_adjust_row_height = True
                print("启用自动行高调整")
                # 立即执行一次调整
                self.adjust_page_size_to_window()
            else:
                # 禁用自动调整，使用固定行高
                self.auto_adjust_row_height = False
                if mode == "小":
                    fixed_height = 25
                elif mode == "中":
                    fixed_height = 35
                elif mode == "大":
                    fixed_height = 50
                else:
                    fixed_height = 30

                self.base_row_height = fixed_height
                print(f"设置固定行高: {fixed_height}px")

                # 应用新的行高
                self.apply_row_height_to_table(fixed_height)

                # 重新计算页面大小（但不调整行高）
                if self.auto_adjust_page_size:
                    self.adjust_page_size_to_window()

        except Exception as e:
            print(f"改变行高模式失败: {e}")

    def create_table_item_with_font(self, text, column_index, text_color):
        """创建带有适当字体大小的表格项"""
        item = QTableWidgetItem(text)
        item.setTextAlignment(Qt.AlignCenter)
        item.setForeground(text_color)

        # 根据列索引设置不同的字体大小
        font = item.font()
        if column_index in [0, 1, 3, 4]:  # 时间列(0)、股票列(1)、新闻内容列(3)、市场情绪列(4)
            # 根据当前行高动态调整字体大小
            if hasattr(self, 'base_row_height'):
                if self.base_row_height <= 30:
                    base_font_size = 12
                elif self.base_row_height <= 40:
                    base_font_size = 14
                elif self.base_row_height <= 50:
                    base_font_size = 16
                else:
                    base_font_size = 18
            else:
                base_font_size = 14  # 默认字体大小

            # 为不同列设置不同的字体大小
            if column_index in [0, 4]:  # 时间列(0)、市场情绪列(4)
                font_size = max(6, int((base_font_size // 2) * 1.5))  # 在原基础上增加50%，最小字体大小为6pt
            elif column_index == 3:  # 新闻内容列(3) - 设置更大的字体
                font_size = max(10, int(base_font_size * 1.2))  # 比基础字体大20%，最小字体大小为10pt
            else:  # 股票列(1)
                font_size = base_font_size

            font.setPointSize(font_size)

            # 新闻内容列不设置粗体，其他列设置粗体
            if column_index != 3:
                font.setBold(True)

            item.setFont(font)

        return item

    def update_pagination_info(self):
        """更新分页信息显示"""
        total_news = len(self.news_analyzer.get_news_history())
        self.total_pages = self.calculate_total_pages(total_news)

        # 确保当前页在有效范围内
        if self.current_page > self.total_pages:
            self.current_page = max(1, self.total_pages)

        # 更新页码信息
        self.page_info_label.setText(f"第 {self.current_page} 页 / 共 {self.total_pages} 页")

        # 更新按钮状态
        self.first_page_btn.setEnabled(self.current_page > 1)
        self.prev_page_btn.setEnabled(self.current_page > 1)
        self.next_page_btn.setEnabled(self.current_page < self.total_pages)
        self.last_page_btn.setEnabled(self.current_page < self.total_pages)

        print(f"分页信息更新: 第{self.current_page}页/共{self.total_pages}页, 总记录{total_news}条")

    def get_page_data(self, all_news_sorted):
        """获取当前页的数据"""
        start_index = (self.current_page - 1) * self.page_size
        end_index = start_index + self.page_size
        return all_news_sorted[start_index:end_index]

    def go_to_first_page(self):
        """跳转到首页"""
        if self.current_page != 1:
            self.current_page = 1
            self.refresh_current_page()

    def go_to_prev_page(self):
        """跳转到上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.refresh_current_page()

    def go_to_next_page(self):
        """跳转到下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.refresh_current_page()

    def go_to_last_page(self):
        """跳转到末页"""
        if self.current_page != self.total_pages:
            self.current_page = self.total_pages
            self.refresh_current_page()

    def go_to_page(self):
        """跳转到指定页面"""
        try:
            page_num = int(self.page_input.text())
            if 1 <= page_num <= self.total_pages:
                self.current_page = page_num
                self.refresh_current_page()
                self.page_input.clear()
            else:
                QMessageBox.warning(self, "页码错误", f"请输入1到{self.total_pages}之间的页码")
        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的页码数字")

    def change_page_size(self, new_size):
        """改变每页显示数量"""
        try:
            if new_size == "自动":
                # 启用自动调整
                self.auto_adjust_page_size = True
                print("启用自动页面大小调整")
                # 立即执行一次调整
                self.adjust_page_size_to_window()
            else:
                # 禁用自动调整，使用固定大小
                self.auto_adjust_page_size = False
                self.page_size = int(new_size)
                print(f"设置固定页面大小: {self.page_size}")
                self.current_page = 1  # 重置到第一页
                self.refresh_current_page()
        except ValueError:
            pass

    def refresh_current_page(self):
        """刷新当前页面显示"""
        self.update_news_table([])  # 空列表表示刷新全部



if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon("icon.ico"))
    window = MainWindow()
    sys.exit(app.exec())
