#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试金十数据处理流程
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
modules_path = os.path.join(current_dir, "modules")
if modules_path not in sys.path:
    sys.path.insert(0, modules_path)

from modules.news_analyzer import NewsAnalyzer


def debug_jin10_processing():
    """调试金十数据处理的完整流程"""
    print("🔍 调试金十数据处理流程")
    print("=" * 80)
    
    # 使用用户提供的实际数据
    test_data = [
        "341. 17:16:00",
        "342. **商务部消费促进司负责人解读《成品油流通管理办法》**",
        "343. 金十数据8月1日讯，商务部消费促进司负责人解读《成品油流通管理办法》。明确对相关违规行为的处罚。依据行政许可法、行政处罚法规定，结合国务院文件授权和部门规章设定行政处罚的权限，明确不予备案及撤销备案回执、不予许可、撤销许可、注销许可等具体情形，形成有进有出的管理闭环。此外，针对违反《办法》的相关行为，按照情形严重程度，分档设置行政处罚条款，做到过罚相当。《办法》将于9月1日起实施。下一步，商务部将加强宣传培训，指导地方商务主管部门准确理解和把握《办法》内容，依法履职尽责，推动成品油市场健康发展。",
        "344. [详情](https://flash.jin10.com/detail/20250801171537340800)",
        "345. 17:15:37",
        "346. **信义光能：上半年股东应占溢利7.46亿元 同比减少58.8%**",
        "347. 金十数据8月1日讯，信义光能(00968.HK)发布截至2025年6月30日止6个月的中期业绩，该集团取得收益109.32亿元，同比减少6.5%;公司权益持有人应占溢利7.46亿元，同比减少58.8%;每股基本盈利8.21分，拟派发中期股息每股4.2港分。",
        "348. [详情](https://flash.jin10.com/detail/20250801171335578800)"
    ]
    
    analyzer = NewsAnalyzer()
    
    # 设置股票数据（包含信义光能）
    analyzer.stock_codes = ["00968", "600036", "00700", "09988"]
    analyzer.stock_names = ["信义光能", "招商银行", "腾讯控股", "阿里巴巴"]
    
    print("📋 原始数据:")
    for i, line in enumerate(test_data):
        print(f"{i+1:2d}. {line}")
    
    print("\n" + "=" * 80)
    print("🔧 步骤1: 预处理数据")
    print("=" * 80)
    
    # 步骤1: 预处理
    entries = analyzer._preprocess_jin10_content(test_data)
    
    print(f"预处理结果: 提取到 {len(entries)} 个条目")
    for i, entry in enumerate(entries):
        print(f"条目 {i+1}:")
        print(f"  序号: {entry['sequence_num']}")
        print(f"  时间: {entry['time']}")
        print(f"  内容: {entry['content'][:100]}...")
        print()
    
    print("=" * 80)
    print("🔧 步骤2: 股票匹配")
    print("=" * 80)
    
    # 步骤2: 股票匹配
    matched_news = analyzer.match_jin10_stock_content(test_data)
    
    print(f"股票匹配结果: 找到 {len(matched_news)} 条相关新闻")
    for i, news in enumerate(matched_news):
        print(f"匹配结果 {i+1}:")
        print(f"  时间: {news['news_time']}")
        print(f"  股票: {news['stock']}")
        print(f"  股票类型: {news.get('stock_type', 'N/A')}")
        print(f"  序号: {news.get('sequence_num', 'N/A')}")
        print(f"  内容: {news['content']}")
        print()
    
    print("=" * 80)
    print("🎯 预期结果验证")
    print("=" * 80)
    
    # 验证是否得到预期结果
    expected_found = False
    for news in matched_news:
        if "信义光能" in news['stock'] and "17:15:37" in news['news_time']:
            expected_found = True
            print("✅ 找到预期的信义光能新闻:")
            print(f"   时间: {news['news_time']}")
            print(f"   股票: {news['stock']}")
            print(f"   内容: {news['content']}")
            break
    
    if not expected_found:
        print("❌ 未找到预期的信义光能新闻")
        print("🔍 调试信息:")
        
        # 检查股票名称是否在股票列表中
        if "信义光能" in analyzer.stock_names:
            print("✅ 信义光能在股票名称列表中")
        else:
            print("❌ 信义光能不在股票名称列表中")
        
        # 检查股票代码是否在股票列表中
        if "00968" in analyzer.stock_codes:
            print("✅ 00968在股票代码列表中")
        else:
            print("❌ 00968不在股票代码列表中")
        
        # 手动测试股票匹配
        test_content = "金十数据8月1日讯，信义光能(00968.HK)发布截至2025年6月30日止6个月的中期业绩"
        matched_stocks = analyzer._match_stocks_in_content(test_content)
        print(f"手动测试股票匹配结果: {matched_stocks}")


def debug_stock_matching():
    """专门调试股票匹配功能"""
    print("\n🔍 专门调试股票匹配功能")
    print("=" * 80)
    
    analyzer = NewsAnalyzer()
    analyzer.stock_codes = ["00968", "600036", "00700", "09988"]
    analyzer.stock_names = ["信义光能", "招商银行", "腾讯控股", "阿里巴巴"]
    
    test_contents = [
        "信义光能(00968.HK)发布截至2025年6月30日止6个月的中期业绩",
        "金十数据8月1日讯，信义光能(00968.HK)发布业绩",
        "信义光能上半年业绩公布",
        "00968.HK今日发布财报",
        "招商银行600036今日涨停"
    ]
    
    for i, content in enumerate(test_contents, 1):
        print(f"测试 {i}: {content}")
        matched_stocks = analyzer._match_stocks_in_content(content)
        if matched_stocks:
            for stock in matched_stocks:
                print(f"  ✅ 匹配: {stock['stock']} ({stock['match_type']}) -> {stock['stock_type']}")
        else:
            print("  ❌ 无匹配")
        print()


def debug_preprocessing():
    """专门调试预处理功能"""
    print("\n🔍 专门调试预处理功能")
    print("=" * 80)
    
    test_data = [
        "341. 17:16:00",
        "342. **商务部消费促进司负责人解读《成品油流通管理办法》**",
        "343. 金十数据8月1日讯，商务部消费促进司负责人解读《成品油流通管理办法》。",
        "344. [详情](https://flash.jin10.com/detail/20250801171537340800)",
        "345. 17:15:37",
        "346. **信义光能：上半年股东应占溢利7.46亿元 同比减少58.8%**",
        "347. 金十数据8月1日讯，信义光能(00968.HK)发布截至2025年6月30日止6个月的中期业绩。",
        "348. [详情](https://flash.jin10.com/detail/20250801171335578800)"
    ]
    
    analyzer = NewsAnalyzer()
    
    print("📋 逐步处理过程:")
    print("-" * 40)
    
    i = 0
    while i < len(test_data):
        line = test_data[i].strip()
        print(f"处理行 {i}: {line}")
        
        # 检查是否是时间行
        import re
        time_pattern = r'^(\d+)\.\s+(\d{1,2}:\d{2}:\d{2})\s*$'
        time_match = re.match(time_pattern, line)
        
        if time_match:
            sequence_num = time_match.group(1)
            time_part = time_match.group(2)
            print(f"  ✅ 识别为时间行: 序号={sequence_num}, 时间={time_part}")
            
            # 查找内容行
            j = i + 1
            while j < len(test_data):
                next_line = test_data[j].strip()
                print(f"    检查行 {j}: {next_line}")
                
                if not next_line:
                    print("      跳过空行")
                    j += 1
                    continue
                
                if re.match(r'^\d+\.\s+\d{1,2}:\d{2}:\d{2}', next_line):
                    print("      遇到下一个时间行，停止")
                    break
                
                if analyzer._is_link_line(next_line):
                    print("      识别为链接行，过滤")
                    j += 1
                    continue
                
                if analyzer._is_useless_line(next_line):
                    print("      识别为无用行，过滤")
                    j += 1
                    continue
                
                print(f"      ✅ 找到内容行: {next_line}")
                break
            
            i = j
        else:
            print(f"  ❌ 非时间行")
            i += 1
        
        print()


async def main():
    """主函数"""
    try:
        # 调试预处理功能
        debug_preprocessing()
        
        # 调试股票匹配功能
        debug_stock_matching()
        
        # 调试完整处理流程
        debug_jin10_processing()
        
        print("\n🎉 调试完成!")
            
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
