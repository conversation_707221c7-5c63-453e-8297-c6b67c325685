# ///////////////////////////////////////////////////////////////
#
# 新闻分析模块
# 用于爬取财联社新闻并进行股票情绪分析
#
# ///////////////////////////////////////////////////////////////

import asyncio
import pandas as pd
import os
import re
import logging
from datetime import datetime
from typing import List, Dict, Optional
import sys
from .news_database import NewsDatabase
from .crawl4ai import AsyncWebCrawler
from .glm_z1_client import GLMZ1Client, GLMError
import akshare as ak
from .stock_constants import (
    get_all_stock_data, get_stock_codes, get_stock_names,
    get_a_stock_data, get_hk_stock_data, get_stock_info, is_initialized
)


class NewsAnalyzer:
    def __init__(self):
        self.stock_codes = []
        self.stock_names = []
        self.glm_client = None  # 替换 chatbot 为 glm_client
        self.news_history = []  # 存储历史新闻数据
        self.db = NewsDatabase()  # 数据库实例

        # 检查必要模块是否导入成功
        if AsyncWebCrawler is None:
            print("警告: AsyncWebCrawler 未导入，爬虫功能将不可用")
        if GLMZ1Client is None:
            print("警告: GLMZ1Client 未导入，情绪分析功能将不可用")

        # 启动时清理7天前的旧新闻
        self.cleanup_old_news()

        # 检查并设置默认API-KEY（如果数据库中没有的话）
        self.ensure_default_api_key()

        # 不再使用单独的股票数据库，统一使用 news_database.db

    def ensure_default_api_key(self):
        """确保数据库中有默认的API-KEY，如果没有则设置默认值"""
        try:
            # 默认的GLM-Z1-Flash API-KEY
            DEFAULT_API_KEY = "2b7cb3e2559e451789f8ec0d39ed6c71.K8YgHlkjiQWl1wPd"

            # 检查数据库中是否已有API-KEY
            current_token = self.db.get_token()

            if current_token is None:
                print("🔑 数据库中未找到API-KEY，设置默认值...")
                print(f"   默认API-KEY: {DEFAULT_API_KEY[:20]}...{DEFAULT_API_KEY[-10:]}")

                # 保存默认API-KEY到数据库
                success = self.db.save_token(DEFAULT_API_KEY)

                if success:
                    print("✅ 默认API-KEY已设置并保存到数据库")
                else:
                    print("❌ 默认API-KEY保存失败")
            else:
                print(f"✅ 数据库中已有API-KEY: {current_token[:20]}...{current_token[-10:]}")

        except Exception as e:
            print(f"❌ 设置默认API-KEY时出错: {e}")

    def load_stock_data(self) -> bool:
        """使用akshare库获取当天所有国内A股以及港股通的股票名称和代码，失败时使用常量文件"""
        try:
            print("正在使用akshare获取A股和港股通股票数据...")

            # 检查常量文件是否已初始化
            constants_initialized = is_initialized()

            if not constants_initialized:
                print("📋 常量文件未初始化，需要通过akshare首次获取数据进行初始化...")

            # 尝试使用akshare获取数据
            akshare_success = self._try_load_from_akshare()

            if akshare_success:
                if not constants_initialized:
                    print("🎯 首次初始化：使用akshare数据初始化常量文件...")
                    self._initialize_constants()
                else:
                    print("✅ akshare数据获取成功，检查与常量文件的差异...")
                    self._check_and_update_constants()
                return True
            else:
                if not constants_initialized:
                    print("❌ akshare获取失败且常量文件未初始化，无法继续...")
                    return False
                else:
                    print("❌ akshare获取失败，使用常量文件中的数据...")
                    return self._load_from_constants()

        except Exception as e:
            print(f"❌ 股票数据加载过程中出现异常: {e}")
            import traceback
            traceback.print_exc()

            # 检查是否可以回退到常量文件
            if is_initialized():
                print("🔄 回退到常量文件数据...")
                return self._load_from_constants()
            else:
                print("❌ 常量文件未初始化，无法回退...")
                return False

    def _try_load_from_akshare(self) -> bool:
        """尝试从akshare获取股票数据"""
        try:
            # 获取A股股票列表
            print("获取A股股票列表...")
            a_stock_df = ak.stock_zh_a_spot_em()

            if a_stock_df is None or len(a_stock_df) == 0:
                print("❌ 获取A股数据失败")
                return False

            # 提取A股代码和名称
            a_stock_codes = a_stock_df['代码'].astype(str).tolist()
            a_stock_names = a_stock_df['名称'].tolist()

            print(f"✅ 获取到 {len(a_stock_codes)} 只A股")

            # 获取港股通股票列表
            print("获取港股通股票列表...")
            hk_stock_codes = []
            hk_stock_names = []

            try:
                # 获取港股通股票（包含沪港通和深港通）
                hk_df = ak.stock_hk_ggt_components_em()

                if hk_df is not None and len(hk_df) > 0:
                    hk_stock_codes = hk_df['代码'].astype(str).tolist()
                    hk_stock_names = hk_df['名称'].tolist()

                print(f"✅ 获取到 {len(hk_stock_codes)} 只港股通股票")

            except Exception as hk_e:
                print(f"⚠️ 获取港股通数据失败: {hk_e}，仅使用A股数据")

            # 合并A股和港股通数据
            self.stock_codes = a_stock_codes + hk_stock_codes
            self.stock_names = a_stock_names + hk_stock_names

            # 去掉股票名称中的ST前面的星号*
            self.stock_names = [name.replace('*', '') for name in self.stock_names]

            # 存储akshare获取的数据用于后续比较
            self.akshare_data = {
                'a_stock_codes': a_stock_codes,
                'a_stock_names': a_stock_names,
                'hk_stock_codes': hk_stock_codes,
                'hk_stock_names': hk_stock_names
            }

            print(f"✅ 成功从akshare加载 {len(self.stock_codes)} 只股票信息（A股: {len(a_stock_codes)}, 港股通: {len(hk_stock_codes)}）")
            return True

        except Exception as e:
            print(f"❌ akshare获取数据时出错: {e}")
            return False

    def _load_from_constants(self) -> bool:
        """从常量文件加载股票数据"""
        try:
            print("📁 从常量文件加载股票数据...")

            # 获取常量数据
            all_stock_data = get_all_stock_data()
            self.stock_codes = get_stock_codes()
            self.stock_names = get_stock_names()

            # 获取统计信息
            stock_info = get_stock_info()

            print(f"✅ 成功从常量文件加载 {stock_info['total_count']} 只股票信息")
            print(f"   - A股: {stock_info['a_stock_count']} 只")
            print(f"   - 港股通: {stock_info['hk_stock_count']} 只")
            print(f"   - 数据更新时间: {stock_info['last_updated']}")

            return True

        except Exception as e:
            print(f"❌ 从常量文件加载数据失败: {e}")
            return False

    def get_current_stock_display_info(self) -> dict:
        """
        获取当前股票信息用于显示，确保返回股票名称而不是代码

        Returns:
            dict: 包含股票代码和名称的字典，优先显示股票名称
        """
        try:
            # 由于移除了股票数据库，这里返回默认值
            # 如果需要股票选择功能，可以通过 news_database.db 的 config 表实现
            return {
                'stock_code': '',
                'stock_name': '',
                'display_text': '未选择股票',
                'query_time': ''
            }

        except Exception as e:
            print(f"❌ 获取当前股票显示信息失败: {e}")
            return {
                'stock_code': '',
                'stock_name': '',
                'display_text': '获取失败',
                'query_time': ''
            }

    def _initialize_constants(self):
        """首次初始化常量文件"""
        try:
            if not hasattr(self, 'akshare_data'):
                print("⚠️ 没有akshare数据可用于初始化")
                return

            print("🎯 正在初始化常量文件...")

            # 构建akshare数据字典
            akshare_a_stock_data = {}
            for code, name in zip(self.akshare_data['a_stock_codes'], self.akshare_data['a_stock_names']):
                # 清理股票名称
                clean_name = name.replace('*', '')
                akshare_a_stock_data[code] = clean_name

            akshare_hk_stock_data = {}
            for code, name in zip(self.akshare_data['hk_stock_codes'], self.akshare_data['hk_stock_names']):
                # 清理股票名称
                clean_name = name.replace('*', '')
                akshare_hk_stock_data[code] = clean_name

            # 直接更新常量文件（首次初始化）
            print(f"📝 初始化常量文件，A股: {len(akshare_a_stock_data)} 只，港股通: {len(akshare_hk_stock_data)} 只")
            self._update_constants_file(akshare_a_stock_data, akshare_hk_stock_data, is_initialization=True)

            print("✅ 常量文件初始化完成")

        except Exception as e:
            print(f"❌ 初始化常量文件时出错: {e}")
            import traceback
            traceback.print_exc()

    def _check_and_update_constants(self):
        """检查akshare数据与常量文件的差异，如有差异则更新常量文件"""
        try:
            if not hasattr(self, 'akshare_data'):
                print("⚠️ 没有akshare数据可供比较")
                return

            print("🔍 检查数据差异...")

            # 获取当前常量数据
            current_a_stock_data = get_a_stock_data()
            current_hk_stock_data = get_hk_stock_data()

            # 如果常量文件为空，直接初始化
            if len(current_a_stock_data) == 0 and len(current_hk_stock_data) == 0:
                print("📋 常量文件为空，执行初始化...")
                self._initialize_constants()
                return

            # 构建akshare数据字典
            akshare_a_stock_data = {}
            for code, name in zip(self.akshare_data['a_stock_codes'], self.akshare_data['a_stock_names']):
                # 清理股票名称
                clean_name = name.replace('*', '')
                akshare_a_stock_data[code] = clean_name

            akshare_hk_stock_data = {}
            for code, name in zip(self.akshare_data['hk_stock_codes'], self.akshare_data['hk_stock_names']):
                # 清理股票名称
                clean_name = name.replace('*', '')
                akshare_hk_stock_data[code] = clean_name

            # 检查A股差异
            a_stock_diff = self._compare_stock_data(current_a_stock_data, akshare_a_stock_data, "A股")

            # 检查港股通差异
            hk_stock_diff = self._compare_stock_data(current_hk_stock_data, akshare_hk_stock_data, "港股通")

            # 如果有差异，更新常量文件
            if a_stock_diff['has_diff'] or hk_stock_diff['has_diff']:
                print("📝 发现数据差异，更新常量文件...")
                self._update_constants_file(akshare_a_stock_data, akshare_hk_stock_data, is_initialization=False)
            else:
                print("✅ 数据无差异，无需更新常量文件")

        except Exception as e:
            print(f"❌ 检查数据差异时出错: {e}")

    def _compare_stock_data(self, current_data: dict, new_data: dict, data_type: str) -> dict:
        """比较两个股票数据字典的差异"""
        try:
            # 新增的股票
            added = {k: v for k, v in new_data.items() if k not in current_data}

            # 删除的股票
            removed = {k: v for k, v in current_data.items() if k not in new_data}

            # 名称变更的股票
            changed = {}
            for code in current_data:
                if code in new_data and current_data[code] != new_data[code]:
                    changed[code] = {
                        'old': current_data[code],
                        'new': new_data[code]
                    }

            has_diff = len(added) > 0 or len(removed) > 0 or len(changed) > 0

            if has_diff:
                print(f"📊 {data_type}数据差异统计:")
                if added:
                    print(f"   - 新增: {len(added)} 只")
                    for code, name in list(added.items())[:5]:  # 只显示前5个
                        print(f"     + {code}: {name}")
                    if len(added) > 5:
                        print(f"     ... 还有 {len(added) - 5} 只")

                if removed:
                    print(f"   - 删除: {len(removed)} 只")
                    for code, name in list(removed.items())[:5]:  # 只显示前5个
                        print(f"     - {code}: {name}")
                    if len(removed) > 5:
                        print(f"     ... 还有 {len(removed) - 5} 只")

                if changed:
                    print(f"   - 名称变更: {len(changed)} 只")
                    for code, change in list(changed.items())[:5]:  # 只显示前5个
                        print(f"     ~ {code}: {change['old']} -> {change['new']}")
                    if len(changed) > 5:
                        print(f"     ... 还有 {len(changed) - 5} 只")
            else:
                print(f"✅ {data_type}数据无差异")

            return {
                'has_diff': has_diff,
                'added': added,
                'removed': removed,
                'changed': changed
            }

        except Exception as e:
            print(f"❌ 比较{data_type}数据时出错: {e}")
            return {'has_diff': False, 'added': {}, 'removed': {}, 'changed': {}}

    def _update_constants_file(self, a_stock_data: dict, hk_stock_data: dict, is_initialization: bool = False):
        """更新常量文件"""
        try:
            from datetime import datetime

            # 构建新的常量文件内容
            file_content = self._generate_constants_file_content(a_stock_data, hk_stock_data, is_initialization)

            # 获取常量文件路径
            constants_file_path = os.path.join(os.path.dirname(__file__), 'stock_constants.py')

            # 备份原文件（仅在非初始化时备份）
            if not is_initialization and os.path.exists(constants_file_path):
                backup_path = constants_file_path + f'.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
                import shutil
                shutil.copy2(constants_file_path, backup_path)
                print(f"📋 已备份原常量文件到: {backup_path}")

            # 写入新文件
            with open(constants_file_path, 'w', encoding='utf-8') as f:
                f.write(file_content)

            action = "初始化" if is_initialization else "更新"
            print(f"✅ 常量文件已{action}: {constants_file_path}")
            print(f"   - A股数量: {len(a_stock_data)}")
            print(f"   - 港股通数量: {len(hk_stock_data)}")
            print(f"   - {action}时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        except Exception as e:
            print(f"❌ {'初始化' if is_initialization else '更新'}常量文件时出错: {e}")
            import traceback
            traceback.print_exc()

    def _generate_constants_file_content(self, a_stock_data: dict, hk_stock_data: dict, is_initialization: bool = False) -> str:
        """生成常量文件内容"""
        from datetime import datetime

        action = "初始化" if is_initialization else "更新"

        # 文件头部注释
        header = '''# ///////////////////////////////////////////////////////////////
#
# 股票常量数据文件
# 存储A股和港股通股票代码和名称的常量数据
# 当akshare获取失败时作为备用数据源
#
# 自动{action}时间: {update_time}
# 数据来源: akshare
#
# ///////////////////////////////////////////////////////////////

'''.format(action=action, update_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        # A股数据部分
        a_stock_content = "# A股股票数据 (股票代码: 股票名称)\nA_STOCK_DATA = {\n"
        for code, name in sorted(a_stock_data.items()):
            # 转义引号
            escaped_name = name.replace('"', '\\"').replace("'", "\\'")
            a_stock_content += f'    "{code}": "{escaped_name}",\n'
        a_stock_content += "}\n\n"

        # 港股通数据部分
        hk_stock_content = "# 港股通股票数据 (股票代码: 股票名称)\nHK_STOCK_DATA = {\n"
        for code, name in sorted(hk_stock_data.items()):
            # 转义引号
            escaped_name = name.replace('"', '\\"').replace("'", "\\'")
            hk_stock_content += f'    "{code}": "{escaped_name}",\n'
        hk_stock_content += "}\n\n"

        # 函数定义部分
        a_count = len(a_stock_data)
        hk_count = len(hk_stock_data)
        total_count = a_count + hk_count
        update_date = datetime.now().strftime('%Y-%m-%d')
        action = "初始化" if is_initialization else "自动更新"

        functions_content = f'''def get_all_stock_data():
    """获取所有股票数据（A股+港股通）"""
    all_data = {{}}
    all_data.update(A_STOCK_DATA)
    all_data.update(HK_STOCK_DATA)
    return all_data

def get_stock_codes():
    """获取所有股票代码列表"""
    return list(A_STOCK_DATA.keys()) + list(HK_STOCK_DATA.keys())

def get_stock_names():
    """获取所有股票名称列表"""
    return list(A_STOCK_DATA.values()) + list(HK_STOCK_DATA.values())

def get_a_stock_data():
    """获取A股数据"""
    return A_STOCK_DATA.copy()

def get_hk_stock_data():
    """获取港股通数据"""
    return HK_STOCK_DATA.copy()

def is_initialized():
    """检查常量文件是否已初始化"""
    return len(A_STOCK_DATA) > 0 or len(HK_STOCK_DATA) > 0

# 数据统计信息
STOCK_DATA_INFO = {{
    "a_stock_count": {a_count},
    "hk_stock_count": {hk_count},
    "total_count": {total_count},
    "last_updated": "{update_date}",
    "data_source": "akshare ({action})",
    "initialized": True
}}

def get_stock_info():
    """获取股票数据统计信息"""
    # 动态更新统计信息
    info = STOCK_DATA_INFO.copy()
    info["a_stock_count"] = len(A_STOCK_DATA)
    info["hk_stock_count"] = len(HK_STOCK_DATA)
    info["total_count"] = len(A_STOCK_DATA) + len(HK_STOCK_DATA)
    info["initialized"] = is_initialized()
    return info
'''

        return header + a_stock_content + hk_stock_content + functions_content

    def is_valid_stock(self, stock_info: str) -> bool:
        """直接查询验证股票是否为A股或港股通股票"""
        try:
            # 检查是否在已加载的股票列表中（A股+港股通）
            if stock_info in self.stock_names or stock_info in self.stock_codes:
                print(f"✅ 股票验证通过: {stock_info} 在A股/港股通列表中")
                return True

            # 检查是否是A股代码格式（6位数字）
            if stock_info.isdigit() and len(stock_info) == 6:
                # 检查是否是A股代码范围
                code = int(stock_info)
                if (600000 <= code <= 699999 or  # 沪市主板
                    000000 <= code <= 399999 or  # 深市主板和中小板
                    300000 <= code <= 399999 or  # 创业板
                    800000 <= code <= 899999):   # 北交所
                    print(f"✅ 股票验证通过: {stock_info} 符合A股代码格式")
                    return True

            # 检查是否是港股通代码格式（5位数字）
            if stock_info.isdigit() and len(stock_info) == 5:
                # 港股通代码通常是5位数字，范围大致在00001-99999
                code = int(stock_info)
                if 1 <= code <= 99999:
                    print(f"✅ 股票验证通过: {stock_info} 符合港股通代码格式")
                    return True

            # 检查是否包含常见的A股/港股通关键词
            stock_keywords = ['股份', '有限公司', '集团', '科技', '实业', '控股', '投资', '银行', '保险', '证券']
            for keyword in stock_keywords:
                if keyword in stock_info:
                    print(f"✅ 股票验证通过: {stock_info} 包含股票关键词 '{keyword}'")
                    return True

            print(f"❌ 股票验证失败: {stock_info} 不在A股/港股通列表中且不符合股票格式")
            return False

        except Exception as e:
            print(f"股票验证出错: {e}, 默认通过")
            return True  # 出错时默认通过

    def init_chatbot(self, api_key: str = None) -> bool:
        """初始化GLM-Z1-Flash客户端"""
        if GLMZ1Client is None:
            print("❌ GLMZ1Client 未导入，无法初始化GLM客户端")
            return False

        try:
            print("=== 初始化GLM-Z1-Flash客户端 ===")

            # 如果没有提供api_key，从数据库读取
            if api_key is None:
                api_key = self.db.get_token()  # 复用原有的token字段存储API key
                if api_key is None:
                    print("❌ 未找到API Key，请在设置中配置GLM API Key")
                    return False

            print(f"使用API Key: {api_key[:20]}...")  # 只显示前20个字符

            self.glm_client = GLMZ1Client(api_key)

            # 测试连接
            if self.glm_client.test_connection():
                print("✅ GLM-Z1-Flash客户端初始化成功")
                return True
            else:
                print("❌ GLM-Z1-Flash连接测试失败")
                return False

        except Exception as e:
            print(f"❌ GLM-Z1-Flash客户端初始化失败: {e}")
            return False

    def extract_news_time(self, content_lines: List[str], news_index: int) -> str:
        """从新闻内容中提取时间信息（保留方法以兼容旧代码）"""
        try:
            # 查找新闻前面的时间行（格式如 "10:27" 或 "09:53"）
            time_line = ""
            if news_index > 0:
                prev_line = content_lines[news_index - 1].strip()
                # 匹配时间格式 HH:MM 或 HH:MM:SS
                import re
                time_pattern = r'\d{1,2}:\d{2}(:\d{2})?'
                if re.match(time_pattern, prev_line):
                    time_line = prev_line
                    print(f"提取到时间: {time_line}")
                    return time_line

            print(f"无法提取时间信息，新闻内容: {content_lines[news_index][:50] if news_index < len(content_lines) else '索引超出范围'}...")
            return f"未知时间_{news_index}"

        except Exception as e:
            print(f"提取新闻时间出错: {e}")
            print(f"新闻索引: {news_index}, 新闻内容: {content_lines[news_index] if news_index < len(content_lines) else '索引超出范围'}")
            return f"提取失败_{news_index}"

    def match_stock_content(self, content_lines: List[str]) -> List[Dict]:
        """匹配包含股票代码或股票名称的新闻内容（财联社格式）"""
        matched_lines = []

        for i, line in enumerate(content_lines):
            if not line.strip():  # 跳过空行
                continue

            # 只匹配以 **【 开头的新闻标题
            line_stripped = line.strip()
            if not line_stripped.startswith('**【'):
                continue

            print(f"找到符合格式的新闻: {line[:80]}...")

            # 查找前面的时间行
            time_line = ""
            if i > 0:
                prev_line = content_lines[i - 1].strip()
                # 匹配时间格式 HH:MM:SS
                import re
                time_pattern = r'\d{1,2}:\d{2}:\d{2}'
                if re.match(time_pattern, prev_line):
                    time_line = prev_line
                    print(f"提取到时间: {time_line}")

            # 从新闻内容中提取日期（财联社X月X日）
            date_part = ""
            date_pattern = r'财联社(\d+月\d+日)'
            date_match = re.search(date_pattern, line)
            if date_match:
                date_part = date_match.group(1)
                print(f"提取到日期: {date_part}")
            else:
                # 如果无法提取日期，使用系统当前日期
                from datetime import datetime
                current_date = datetime.now()
                date_part = f"{current_date.month}月{current_date.day}日"
                print(f"无法提取日期，使用系统日期: {date_part}")

            # 组合完整时间：日期 + 时间
            if date_part and time_line:
                combined_time = f"{date_part} {time_line}"
                print(f"组合时间: {combined_time}")
            elif time_line:
                # 如果有时间但没有日期，也使用系统日期
                from datetime import datetime
                current_date = datetime.now()
                fallback_date = f"{current_date.month}月{current_date.day}日"
                combined_time = f"{fallback_date} {time_line}"
                print(f"使用系统日期组合时间: {combined_time}")
            elif date_part:
                combined_time = date_part
            else:
                # 完全无法提取时间，使用系统当前时间
                from datetime import datetime
                current_time = datetime.now()
                combined_time = f"{current_time.month}月{current_time.day}日 {current_time.strftime('%H:%M:%S')}"
                print(f"完全无法提取时间，使用系统时间: {combined_time}")

            # 提取纯新闻内容（去掉链接等）
            news_content = line
            print(f"新闻内容: {news_content[:100]}...")

            # 收集所有匹配的股票
            matched_stocks = self._match_stocks_in_content(line)

            # 根据匹配到的股票数量决定如何创建记录
            if len(matched_stocks) == 0:
                # 没有匹配到股票，跳过
                continue
            elif len(matched_stocks) == 1:
                # 1对1关系：显示具体股票名称
                stock_info = matched_stocks[0]
                matched_lines.append({
                    'content': news_content,
                    'stock': stock_info['stock'],
                    'match_type': stock_info['match_type'],
                    'news_time': combined_time,
                    'matched_stocks': [stock_info['stock']],  # 记录所有匹配的股票
                    'stock_type': stock_info['stock_type']  # 添加股票类型
                })
                print(f"1对1关系，股票: {stock_info['stock']} ({stock_info['stock_type']})")
            else:
                # 多对1关系：显示"多支股票"，判断股票类型组合
                all_stocks = [s['stock'] for s in matched_stocks]
                stock_types = [s['stock_type'] for s in matched_stocks]
                combined_stock_type = self._determine_combined_stock_type(stock_types)

                matched_lines.append({
                    'content': news_content,
                    'stock': '多支股票',
                    'match_type': 'multiple',
                    'news_time': combined_time,
                    'matched_stocks': all_stocks,  # 记录所有匹配的股票
                    'stock_type': combined_stock_type  # 添加组合股票类型
                })
                print(f"多对1关系，匹配到 {len(matched_stocks)} 支股票: {', '.join(all_stocks)} ({combined_stock_type})")

        return matched_lines

    def match_jin10_stock_content(self, content_lines: List[str]) -> List[Dict]:
        """匹配包含股票代码或股票名称的金十数据新闻内容"""
        print(f"🔍 DEBUG: match_jin10_stock_content 开始，输入 {len(content_lines)} 行数据")
        matched_lines = []
        import re
        from datetime import datetime

        # 首先预处理内容，提取有效的新闻条目
        print("🔍 DEBUG: 开始预处理金十数据...")
        processed_entries = self._preprocess_jin10_content(content_lines)
        print(f"🔍 DEBUG: 预处理完成，提取到 {len(processed_entries)} 个有效条目")

        for i, entry in enumerate(processed_entries, 1):
            sequence_num = entry['sequence_num']
            time_part = entry['time']
            news_content = entry['content']

            print(f"🔍 DEBUG: 处理条目 {i}/{len(processed_entries)}")
            print(f"  序号: {sequence_num}, 时间: {time_part}")
            print(f"  内容: {news_content[:100]}...")

            # 使用当前日期组合完整时间
            current_date = datetime.now()
            combined_time = f"{current_date.month}月{current_date.day}日 {time_part}"
            print(f"  组合时间: {combined_time}")

            # 收集所有匹配的股票
            print(f"🔍 DEBUG: 开始匹配股票...")
            matched_stocks = self._match_stocks_in_content(news_content)
            print(f"🔍 DEBUG: 匹配结果 - 找到 {len(matched_stocks)} 只股票")

            # 根据匹配到的股票数量决定如何创建记录
            if len(matched_stocks) == 0:
                # 没有匹配到股票，跳过
                print("未匹配到股票，跳过")
            elif len(matched_stocks) == 1:
                # 1对1关系：显示具体股票名称
                stock_info = matched_stocks[0]
                matched_lines.append({
                    'content': news_content,
                    'stock': stock_info['stock'],
                    'match_type': stock_info['match_type'],
                    'news_time': combined_time,
                    'matched_stocks': [stock_info['stock']],  # 记录所有匹配的股票
                    'sequence_num': sequence_num,  # 金十数据特有的序号
                    'stock_type': stock_info['stock_type']  # 添加股票类型
                })
                print(f"1对1关系，股票: {stock_info['stock']} ({stock_info['stock_type']})")
            else:
                # 多对1关系：显示"多支股票"，判断股票类型组合
                all_stocks = [s['stock'] for s in matched_stocks]
                stock_types = [s['stock_type'] for s in matched_stocks]
                combined_stock_type = self._determine_combined_stock_type(stock_types)

                matched_lines.append({
                    'content': news_content,
                    'stock': '多支股票',
                    'match_type': 'multiple',
                    'news_time': combined_time,
                    'matched_stocks': all_stocks,  # 记录所有匹配的股票
                    'sequence_num': sequence_num,  # 金十数据特有的序号
                    'stock_type': combined_stock_type  # 添加组合股票类型
                })
                print(f"多对1关系，匹配到 {len(matched_stocks)} 支股票: {', '.join(all_stocks)} ({combined_stock_type})")

        return matched_lines

    def _preprocess_jin10_content(self, content_lines: List[str]) -> List[Dict]:
        """预处理金十数据内容，提取有效的新闻条目"""
        import re
        entries = []

        i = 0
        while i < len(content_lines):
            line = content_lines[i].strip()
            if not line:  # 跳过空行
                i += 1
                continue

            # 匹配金十数据时间格式：支持两种格式
            # 格式1：数字序号. 时间格式，例如：182. 18:57:36
            # 格式2：纯时间格式，例如：19:11:22
            time_pattern1 = r'^(\d+)\.\s+(\d{1,2}:\d{2}:\d{2})\s*$'  # 带序号
            time_pattern2 = r'^(\d{1,2}:\d{2}:\d{2})\s*$'            # 纯时间

            time_match1 = re.match(time_pattern1, line)
            time_match2 = re.match(time_pattern2, line)

            # DEBUG: 检查每一行是否匹配时间格式
            if line and len(line) < 50:  # 只检查短行，避免输出太多
                match_result = "✅" if (time_match1 or time_match2) else "❌"
                print(f"🔍 DEBUG: 检查行 {i}: '{line}' -> 匹配: {match_result}")

            if time_match1:
                # 格式1：带序号的时间
                sequence_num = time_match1.group(1)
                time_part = time_match1.group(2)
                print(f"找到带序号的时间行: 序号{sequence_num}, 时间{time_part}")
            elif time_match2:
                # 格式2：纯时间
                sequence_num = str(i)  # 使用行号作为序号
                time_part = time_match2.group(1)
                print(f"找到纯时间行: 行号{sequence_num}, 时间{time_part}")
            else:
                i += 1
                continue

            # 收集所有相关的内容行，直到遇到下一个时间行或链接行
            content_lines_list = []
            j = i + 1

            while j < len(content_lines):
                    next_line = content_lines[j].strip()

                    # 跳过空行
                    if not next_line:
                        j += 1
                        continue

                    # 如果遇到下一个时间行，停止（支持两种格式）
                    if (re.match(r'^\d+\.\s+\d{1,2}:\d{2}:\d{2}', next_line) or
                        re.match(r'^\d{1,2}:\d{2}:\d{2}\s*$', next_line)):
                        print(f"遇到下一个时间行，停止收集: {next_line}")
                        break

                    # 过滤掉包含链接的行
                    if self._is_link_line(next_line):
                        print(f"过滤链接行: {next_line}")
                        j += 1
                        continue

                    # 过滤掉VIP标识行
                    if next_line.strip() == "VIP":
                        print(f"过滤VIP行: {next_line}")
                        j += 1
                        continue

                    # 过滤掉其他无用行
                    if self._is_useless_line(next_line):
                        print(f"过滤无用行: {next_line}")
                        j += 1
                        continue

                    # 收集有效的内容行
                    print(f"收集内容行: {next_line}")
                    content_lines_list.append(next_line)
                    j += 1

            # 处理收集到的内容行
            if content_lines_list:
                # 查找最长的内容行作为主要新闻内容
                main_content = ""
                for content_line in content_lines_list:
                    # 跳过标题行（通常以数字序号开头且包含**标记）
                    if re.match(r'^\d+\.\s+\*\*.*\*\*\s*$', content_line):
                        print(f"跳过标题行: {content_line}")
                        continue

                    # 选择最长的内容行作为新闻内容
                    if len(content_line) > len(main_content):
                        main_content = content_line

                # 如果没有找到合适的内容行，使用第一个非标题行
                if not main_content and content_lines_list:
                    for content_line in content_lines_list:
                        if not re.match(r'^\d+\.\s+\*\*.*\*\*\s*$', content_line):
                            main_content = content_line
                            break

                # 如果还是没有找到，使用第一行
                if not main_content:
                    main_content = content_lines_list[0]

                if main_content:
                    entries.append({
                        'sequence_num': sequence_num,
                        'time': time_part,
                        'content': main_content
                    })
                    print(f"成功提取条目: 序号{sequence_num}, 时间{time_part}")
                    print(f"  内容: {main_content[:100]}...")

            # 继续处理下一个序号
            i = j

        print(f"预处理完成，提取到 {len(entries)} 个有效条目")
        return entries

    def _is_link_line(self, line: str) -> bool:
        """判断是否是链接行"""
        import re
        # 匹配包含链接的行，如 [详情](https://...)
        link_patterns = [
            r'\[详情\]\(https?://[^\)]+\)',
            r'\[.*?\]\(https?://[^\)]+\)',
            r'https?://[^\s]+',
            r'flash\.jin10\.com',
            r'www\.jin10\.com'
        ]

        for pattern in link_patterns:
            if re.search(pattern, line):
                return True
        return False

    def _is_useless_line(self, line: str) -> bool:
        """判断是否是无用行"""
        useless_patterns = [
            r'^打开金十数据APP$',
            r'^扫码查看小程序$',
            r'^扫码查看$',
            r'^金十数据APP$',
            r'^iOS & 安卓版$',
            r'^精品$',
            r'^资讯$',
            r'^数据$',
            r'^视频$',
            r'^直播$',
            r'^其他$',
            r'^友链$',
            r'^关注我们：$',
            r'^联系我们：$',
            r'^微信扫码',
            r'^©.*Jin10\.com',
            r'^\s*$',  # 空行
            r'^利[多空]\s+[金银原油]+$',  # 如：利多 金银 原油
            r'^前值\s+[\d\.\%\-]+$',
            r'^预期\s+[\d\.\%\-\-]+$',
            r'^公布\s+[\d\.\%\-]+$'
        ]

        for pattern in useless_patterns:
            if re.match(pattern, line):
                return True
        return False

    def _match_stocks_in_content(self, content: str) -> List[Dict]:
        """在内容中匹配股票代码和名称的通用方法"""
        print(f"🔍 DEBUG: _match_stocks_in_content 开始")
        print(f"🔍 DEBUG: 内容长度: {len(content)}, 前50字符: {content[:50]}...")
        print(f"🔍 DEBUG: 可用股票名称数量: {len(self.stock_names)}")

        matched_stocks = []
        import re

        # 首先检查股票名称（优先匹配名称）
        print(f"🔍 DEBUG: 开始检查股票名称匹配...")
        name_matches = 0
        for name in self.stock_names:
            if len(name) > 1 and name in content:
                name_matches += 1
                print(f"🔍 DEBUG: 找到股票名称匹配: {name}")

                # 获取对应的股票代码判断类型
                try:
                    name_index = self.stock_names.index(name)
                    corresponding_code = self.stock_codes[name_index]
                    stock_type = self._determine_stock_type_by_code(corresponding_code)
                    print(f"🔍 DEBUG: 股票代码: {corresponding_code}, 类型: {stock_type}")
                except (ValueError, IndexError):
                    stock_type = 'A'  # 默认为A股
                    print(f"🔍 DEBUG: 无法找到对应代码，使用默认类型: {stock_type}")

                matched_stocks.append({
                    'stock': name,
                    'match_type': 'name',
                    'stock_type': stock_type
                })
                print(f"✅ 匹配到股票名称: {name} ({stock_type})")

        print(f"🔍 DEBUG: 股票名称匹配完成，找到 {name_matches} 个匹配")

        # 然后检查股票代码（只有在没有匹配到名称时才匹配代码）
        if not matched_stocks:
            print(f"🔍 DEBUG: 未找到股票名称匹配，开始检查股票代码...")
            code_matches = 0
            for code in self.stock_codes:
                # A股代码：6位数字
                if len(code) == 6 and code.isdigit() and code in content:
                    matched_stocks.append({
                        'stock': code,
                        'match_type': 'code',
                        'stock_type': 'A'
                    })
                    print(f"匹配到A股代码: {code}")
                # 港股通代码：5位数字，可能带.HK后缀
                elif len(code) == 5 and code.isdigit():
                    # 检查是否匹配纯5位数字代码
                    if code in content:
                        matched_stocks.append({
                            'stock': code,
                            'match_type': 'code',
                            'stock_type': '港'
                        })
                        print(f"匹配到港股通代码: {code}")
                    # 检查是否匹配带.HK后缀的格式
                    elif f"{code}.HK" in content:
                        matched_stocks.append({
                            'stock': code,
                            'match_type': 'code',
                            'stock_type': '港'
                        })
                        print(f"匹配到港股通代码（带.HK）: {code}.HK")
                    # 检查是否匹配括号格式，如(02577.HK)
                    elif f"({code}.HK)" in content:
                        matched_stocks.append({
                            'stock': code,
                            'match_type': 'code',
                            'stock_type': '港'
                        })
                        print(f"匹配到港股通代码（括号格式）: ({code}.HK)")

        # 检查股票名称（长度大于1的名称）
        for name in self.stock_names:
            if len(name) > 1 and name in content:
                # 避免重复匹配（如果已经通过代码匹配了同一只股票）
                already_matched = False

                # 获取当前股票名称对应的股票代码
                try:
                    name_index = self.stock_names.index(name)
                    corresponding_code = self.stock_codes[name_index]
                except (ValueError, IndexError):
                    corresponding_code = None

                # 检查是否已经匹配了相同的股票（通过代码或名称）
                for matched in matched_stocks:
                    if matched['stock'] == name or matched['stock'] == corresponding_code:
                        already_matched = True
                        break

                if not already_matched:
                    # 根据对应的股票代码判断类型
                    stock_type = self._determine_stock_type_by_code(corresponding_code)
                    matched_stocks.append({
                        'stock': name,
                        'match_type': 'name',
                        'stock_type': stock_type
                    })
                    print(f"匹配到股票名称: {name} ({stock_type})")

        print(f"🔍 DEBUG: _match_stocks_in_content 完成，总共找到 {len(matched_stocks)} 只股票")
        if matched_stocks:
            for stock in matched_stocks:
                print(f"🔍 DEBUG: 匹配结果 - {stock['stock']} ({stock['match_type']}) -> {stock['stock_type']}")

        return matched_stocks

    def _determine_stock_type_by_code(self, code: str) -> str:
        """根据股票代码判断股票类型"""
        if not code:
            return 'A股'  # 默认为A股

        if len(code) == 6 and code.isdigit():
            return 'A股'
        elif len(code) == 5 and code.isdigit():
            return '港股'
        else:
            return 'A股'  # 默认为A股

    def _determine_combined_stock_type(self, stock_types: List[str]) -> str:
        """根据多个股票类型确定组合类型"""
        unique_types = set(stock_types)

        if len(unique_types) == 1:
            # 只有一种类型
            return list(unique_types)[0]
        elif 'A股' in unique_types and '港股' in unique_types:
            # 既有A股又有港股
            return '港A'
        else:
            # 其他情况，默认返回第一个类型
            return stock_types[0] if stock_types else 'A股'

    def filter_news_lines_by_time(self, content_lines: List[str], latest_db_time: str) -> List[str]:
        """在股票匹配之前，根据数据库最新时间过滤财联社新闻行"""
        try:
            from datetime import datetime

            # 将数据库最新时间转换为datetime对象
            try:
                # 首先尝试标准格式
                latest_dt = datetime.strptime(latest_db_time, "%Y-%m-%d %H:%M:%S")
                print(f"数据库最新时间转换成功（标准格式）: {latest_dt}")
            except ValueError:
                try:
                    # 如果标准格式失败，尝试转换中文格式
                    latest_sortable = self.convert_to_sortable_timestamp(latest_db_time)
                    latest_dt = datetime.strptime(latest_sortable, "%Y-%m-%d %H:%M:%S")
                    print(f"数据库最新时间转换成功（中文格式转换）: {latest_dt}")
                except ValueError:
                    print(f"数据库时间格式无法解析: {latest_db_time}，跳过时间过滤")
                    return content_lines

            filtered_lines = []
            i = 0

            while i < len(content_lines):
                line = content_lines[i].strip()

                # 只处理以 **【 开头的新闻标题
                if line.startswith('**【'):
                    # 查找前面的时间行
                    time_line = ""
                    if i > 0:
                        prev_line = content_lines[i - 1].strip()
                        # 匹配时间格式 HH:MM:SS
                        import re
                        time_pattern = r'\d{1,2}:\d{2}:\d{2}'
                        if re.match(time_pattern, prev_line):
                            time_line = prev_line

                    # 从新闻内容中提取日期（财联社X月X日）
                    date_part = ""
                    date_pattern = r'财联社(\d+月\d+日)'
                    date_match = re.search(date_pattern, line)
                    if date_match:
                        date_part = date_match.group(1)
                    else:
                        # 如果无法提取日期，使用系统当前日期
                        current_date = datetime.now()
                        date_part = f"{current_date.month}月{current_date.day}日"

                    # 组合完整时间：日期 + 时间
                    if date_part and time_line:
                        combined_time = f"{date_part} {time_line}"
                    elif time_line:
                        # 如果有时间但没有日期，也使用系统日期
                        current_date = datetime.now()
                        fallback_date = f"{current_date.month}月{current_date.day}日"
                        combined_time = f"{fallback_date} {time_line}"
                    elif date_part:
                        combined_time = date_part
                    else:
                        # 完全无法提取时间，使用系统当前时间
                        current_time = datetime.now()
                        combined_time = f"{current_time.month}月{current_time.day}日 {current_time.strftime('%H:%M:%S')}"

                    # 将新闻时间转换为可比较的时间戳
                    sortable_time = self.convert_to_sortable_timestamp(combined_time)

                    try:
                        news_dt = datetime.strptime(sortable_time, "%Y-%m-%d %H:%M:%S")

                        # 只保留比数据库最新时间更新的新闻
                        if news_dt > latest_dt:
                            # 保留时间行（如果存在）
                            if time_line and i > 0:
                                filtered_lines.append(content_lines[i - 1])
                            # 保留新闻行
                            filtered_lines.append(content_lines[i])
                            print(f"保留新闻行: {combined_time} > {latest_db_time}")
                        else:
                            print(f"过滤旧新闻行: {combined_time} <= {latest_db_time}")

                    except ValueError as e:
                        print(f"新闻时间格式无法解析: {combined_time} -> {sortable_time}, 错误: {e}")
                        # 时间格式有问题的新闻也保留，避免丢失
                        if time_line and i > 0:
                            filtered_lines.append(content_lines[i - 1])
                        filtered_lines.append(content_lines[i])
                else:
                    # 非新闻行直接保留
                    filtered_lines.append(content_lines[i])

                i += 1

            return filtered_lines

        except Exception as e:
            print(f"新闻行时间过滤出错: {e}")
            # 出错时返回所有新闻行，避免丢失数据
            return content_lines

    def filter_jin10_news_lines_by_time(self, content_lines: List[str], latest_db_time: str) -> List[str]:
        """在股票匹配之前，根据数据库最新时间过滤金十数据新闻行"""
        try:
            from datetime import datetime
            import re

            # 将数据库最新时间转换为datetime对象
            try:
                # 首先尝试标准格式
                latest_dt = datetime.strptime(latest_db_time, "%Y-%m-%d %H:%M:%S")
                print(f"数据库最新时间转换成功（标准格式）: {latest_dt}")
            except ValueError:
                try:
                    # 如果标准格式失败，尝试转换中文格式
                    latest_sortable = self.convert_to_sortable_timestamp(latest_db_time)
                    latest_dt = datetime.strptime(latest_sortable, "%Y-%m-%d %H:%M:%S")
                    print(f"数据库最新时间转换成功（中文格式转换）: {latest_dt}")
                except ValueError:
                    print(f"数据库时间格式无法解析: {latest_db_time}，跳过时间过滤")
                    return content_lines

            filtered_lines = []
            i = 0

            while i < len(content_lines):
                line = content_lines[i].strip()
                if not line:  # 跳过空行
                    filtered_lines.append(content_lines[i])
                    i += 1
                    continue

                # 匹配金十数据时间格式：支持两种格式
                # 格式1：数字序号. 时间格式，例如：182. 18:57:36
                # 格式2：纯时间格式，例如：19:11:22
                time_pattern1 = r'^(\d+)\.\s+(\d{1,2}:\d{2}:\d{2})\s*$'  # 带序号
                time_pattern2 = r'^(\d{1,2}:\d{2}:\d{2})\s*$'            # 纯时间

                time_match1 = re.match(time_pattern1, line)
                time_match2 = re.match(time_pattern2, line)

                if time_match1:
                    # 格式1：带序号的时间
                    sequence_num = time_match1.group(1)
                    time_part = time_match1.group(2)
                    print(f"🔍 DEBUG: 过滤检查带序号时间行: 序号{sequence_num}, 时间{time_part}")

                    # 检查下一行是否是新闻内容
                    if i + 1 < len(content_lines):
                        next_line = content_lines[i + 1].strip()
                        if next_line and not re.match(r'^\d+\.\s+\d{1,2}:\d{2}:\d{2}', next_line):
                            # 下一行是新闻内容，组合完整时间
                            current_date = datetime.now()
                            combined_time = f"{current_date.month}月{current_date.day}日 {time_part}"

                            if self._should_keep_jin10_news(combined_time, latest_dt, latest_db_time):
                                filtered_lines.append(content_lines[i])      # 时间行
                                filtered_lines.append(content_lines[i + 1])  # 内容行

                            # 跳过已处理的内容行
                            i += 2
                            continue

                elif time_match2:
                    # 格式2：纯时间
                    time_part = time_match2.group(1)
                    print(f"🔍 DEBUG: 过滤检查纯时间行: 时间{time_part}")

                    # 收集后续的内容行，直到遇到下一个时间行
                    content_lines_to_check = []
                    j = i + 1

                    while j < len(content_lines):
                        next_line = content_lines[j].strip()
                        if not next_line:
                            j += 1
                            continue

                        # 如果遇到下一个时间行，停止
                        if (re.match(r'^\d+\.\s+\d{1,2}:\d{2}:\d{2}', next_line) or
                            re.match(r'^\d{1,2}:\d{2}:\d{2}\s*$', next_line)):
                            break

                        content_lines_to_check.append(j)
                        j += 1

                    # 组合完整时间
                    current_date = datetime.now()
                    combined_time = f"{current_date.month}月{current_date.day}日 {time_part}"

                    if self._should_keep_jin10_news(combined_time, latest_dt, latest_db_time):
                        # 保留时间行和所有相关内容行
                        filtered_lines.append(content_lines[i])  # 时间行
                        for content_idx in content_lines_to_check:
                            filtered_lines.append(content_lines[content_idx])

                    # 跳过已处理的内容行
                    i = j
                    continue

                # 非匹配行直接保留
                filtered_lines.append(content_lines[i])
                i += 1

            return filtered_lines

        except Exception as e:
            print(f"金十数据新闻行时间过滤出错: {e}")
            # 出错时返回所有新闻行，避免丢失数据
            return content_lines

    def _should_keep_jin10_news(self, combined_time: str, latest_dt, latest_db_time: str) -> bool:
        """判断金十数据新闻是否应该保留"""
        try:
            from datetime import datetime

            # 将新闻时间转换为可比较的时间戳
            sortable_time = self.convert_to_sortable_timestamp(combined_time)
            news_dt = datetime.strptime(sortable_time, "%Y-%m-%d %H:%M:%S")

            # 只保留比数据库最新时间更新的新闻
            if news_dt > latest_dt:
                print(f"🔍 DEBUG: 保留金十数据新闻: {combined_time} > {latest_db_time}")
                return True
            else:
                print(f"🔍 DEBUG: 过滤金十数据旧新闻: {combined_time} <= {latest_db_time}")
                return False

        except ValueError as e:
            print(f"🔍 DEBUG: 金十数据时间格式无法解析: {combined_time}, 错误: {e}")
            # 时间格式有问题的新闻也保留，避免丢失
            return True

    def filter_news_by_time(self, matched_news: List[Dict], latest_db_time: str) -> List[Dict]:
        """根据数据库最新时间过滤新闻，只保留更新的新闻"""
        try:
            from datetime import datetime

            # 将数据库最新时间转换为datetime对象
            try:
                # 首先尝试标准格式
                latest_dt = datetime.strptime(latest_db_time, "%Y-%m-%d %H:%M:%S")
                print(f"数据库最新时间转换成功（标准格式）: {latest_dt}")
            except ValueError:
                try:
                    # 如果标准格式失败，尝试转换中文格式
                    latest_sortable = self.convert_to_sortable_timestamp(latest_db_time)
                    latest_dt = datetime.strptime(latest_sortable, "%Y-%m-%d %H:%M:%S")
                    print(f"数据库最新时间转换成功（中文格式转换）: {latest_dt}")
                except ValueError:
                    print(f"数据库时间格式无法解析: {latest_db_time}，跳过时间过滤")
                    return matched_news

            filtered_news = []

            for news_item in matched_news:
                news_time = news_item.get('news_time', '')

                # 将新闻时间转换为可比较的时间戳
                sortable_time = self.convert_to_sortable_timestamp(news_time)

                try:
                    news_dt = datetime.strptime(sortable_time, "%Y-%m-%d %H:%M:%S")

                    # 只保留比数据库最新时间更新的新闻
                    if news_dt > latest_dt:
                        filtered_news.append(news_item)
                        print(f"保留新闻: {news_time} (转换后: {sortable_time}) > {latest_db_time}")
                    else:
                        print(f"过滤旧新闻: {news_time} (转换后: {sortable_time}) <= {latest_db_time}")

                except ValueError as e:
                    print(f"新闻时间格式无法解析: {news_time} -> {sortable_time}, 错误: {e}")
                    # 时间格式有问题的新闻也保留，避免丢失
                    filtered_news.append(news_item)

            return filtered_news

        except Exception as e:
            print(f"时间过滤出错: {e}")
            # 出错时返回所有新闻，避免丢失数据
            return matched_news

    def verify_stock_validity(self, stock_info: str, news_content: str) -> str:
        """过滤股价既定涨跌事实（股票已通过match_stock_content验证）"""

        # 注意：stock_info已经通过match_stock_content函数验证，无需再次验证股票类型
        print(f"开始事实过滤验证 - 股票: {stock_info}")

        # 过滤股价既定涨跌事实
        fact_filter_prompt = f"""以下是一段新闻内容：

{news_content}

请判断这段新闻是否属于以下情况之一：
1. 股价既定涨跌事实（如"XX股票今日上涨X%"、"XX股票跌停"、"XX股票创新高"、"股价涨幅XX%"等已经发生的股价变动）
2. 业绩/产品评价或未形成股价事实的消息（如"发布新产品"、"业绩预告"、"政策利好"、"合作协议"、"业绩优秀"、"产品获奖"等关于公司基本面的评价或可能影响未来股价的消息）

注意：
- 只有直接描述股价变动的内容才算"股价既定涨跌事实"
- 关于业绩好坏、产品优劣的评价应该保留，即使是既定的评价
- 政策、合作、技术等消息应该保留

重要：请严格按照要求，只能回答以下两个选项之一：
- 如果是股价既定涨跌事实，请回答"股价既定事实"
- 如果是业绩/产品评价或未形成股价事实的消息，请回答"应该保留"

请只回答"股价既定事实"或"应该保留"，不要有任何其他内容。"""

        # 检查GLM客户端是否已初始化，用于事实过滤
        if not self.glm_client:
            print("GLM客户端未初始化，跳过事实过滤")
            return "有效"  # 如果GLM客户端未初始化，默认通过验证

        max_attempts = 2  # 最大重试次数

        # 第二步：过滤既定涨跌事实
        print(f"第二步：过滤既定涨跌事实")
        for attempt in range(max_attempts):
            try:
                response = self.glm_client.chat(
                    prompt=fact_filter_prompt,
                    system_prompt="你是一个专业的新闻分析师，严格按照要求只回答指定的选项。",
                    temperature=0.1
                )

                # 处理GLM-Z1-Flash的<think>标签，提取实际答案
                response = response.strip()
                if '</think>' in response:
                    think_end = response.find('</think>')
                    if think_end != -1:
                        actual_answer = response[think_end + 8:].strip()
                    else:
                        actual_answer = response
                else:
                    actual_answer = response

                print(f"GLM事实过滤原始响应长度: {len(response)}")
                print(f"GLM事实过滤实际答案: '{actual_answer}'")

                # 精确匹配指定选项
                if actual_answer == "股价既定事实" or "股价既定事实" in actual_answer:
                    print(f"新闻被过滤: 属于股价既定涨跌事实")
                    return "股价既定事实"
                elif actual_answer == "应该保留" or "应该保留" in actual_answer:
                    print(f"新闻通过过滤: 属于业绩/产品评价或未形成股价事实的消息")
                    return "有效"
                else:
                    print(f"GLM响应格式不正确: '{actual_answer}'")
                    if attempt < max_attempts - 1:
                        print(f"重试第 {attempt + 2} 次...")
                        continue
                    else:
                        print("达到最大重试次数，事实过滤失败，默认为'应该保留'")
                        return "有效"

            except GLMError as e:
                print(f"GLM事实过滤出错 (尝试 {attempt + 1}): {e}")
                if attempt < max_attempts - 1:
                    print(f"重试第 {attempt + 2} 次...")
                    continue
                else:
                    print("达到最大重试次数，事实过滤失败，默认为'应该保留'")
                    return "有效"

        return "有效"  # 默认为"应该保留"，即保留新闻做后续分析

    def convert_to_sortable_timestamp(self, news_time: str) -> str:
        """将中文时间格式转换为可排序的时间戳格式"""
        try:
            import re
            from datetime import datetime

            # 处理不同的时间格式
            if "月" in news_time and "日" in news_time:
                # 格式如 "6月30日 11:08:41" 或 "6月30日"
                # 提取月、日、时间部分
                pattern = r'(\d+)月(\d+)日(?:\s+(\d{1,2}:\d{2}:\d{2}))?'
                match = re.match(pattern, news_time)

                if match:
                    month = int(match.group(1))
                    day = int(match.group(2))
                    time_part = match.group(3) if match.group(3) else "00:00:00"

                    # 使用当前年份
                    current_year = datetime.now().year

                    # 构造标准时间格式
                    timestamp_str = f"{current_year}-{month:02d}-{day:02d} {time_part}"

                    # 验证时间格式并转换为datetime对象
                    dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")

                    # 返回可排序的时间戳字符串
                    return dt.strftime("%Y-%m-%d %H:%M:%S")

            elif ":" in news_time:
                # 格式如 "11:08:41"，使用当前日期
                current_date = datetime.now()
                timestamp_str = f"{current_date.year}-{current_date.month:02d}-{current_date.day:02d} {news_time}"
                dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
                return dt.strftime("%Y-%m-%d %H:%M:%S")
            else:
                # 无法解析的格式，使用当前时间
                return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        except Exception as e:
            print(f"时间格式转换出错: {e}, 原始时间: {news_time}")
            # 转换失败时使用当前时间
            return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def analyze_sentiment(self, news_content: str, stock_info: str) -> str:
        """使用GLM-Z1-Flash分析新闻对股票的市场情绪"""
        if not self.glm_client:
            return "未知"

        try:
            # 使用GLM客户端的内置情感分析功能
            sentiment = self.glm_client.sentiment_analysis(news_content)
            print(f"GLM情绪分析结果: {sentiment}")
            return sentiment

        except GLMError as e:
            print(f"GLM情绪分析失败: {e}")
            return "未知"

    async def crawl_news(self, url: str = "https://www.cls.cn/telegraph") -> List[str]:
        """爬取新闻内容"""
        if AsyncWebCrawler is None:
            print("错误: AsyncWebCrawler 未导入，无法执行爬虫功能")
            return []

        try:
            async with AsyncWebCrawler() as crawler:
                result = await crawler.arun(url=url)
                content_lines = result.markdown.split('\n')

                # 返回所有非空行，不再筛选'新闻精选'
                news_lines = []
                for line in content_lines:
                    if line.strip():  # 只过滤掉空行
                        news_lines.append(line)

                print(f"爬取到 {len(news_lines)} 行内容")
                print("=" * 80)
                print("爬取到的新闻内容:")
                print("=" * 80)
                for i, line in enumerate(news_lines, 1):
                    print(f"{i:3d}. {line}")
                print("=" * 80)

                return news_lines
        except Exception as e:
            print(f"爬取新闻失败: {e}")
            return []

    async def crawl_jin10_news(self, url: str = "https://www.jin10.com/index.html") -> List[str]:
        """爬取金十数据新闻内容"""
        if AsyncWebCrawler is None:
            print("错误: AsyncWebCrawler 未导入，无法执行爬虫功能")
            return []

        try:
            from .crawl4ai import CrawlerRunConfig, CacheMode

            # 创建专门针对金十数据的爬取配置
            config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,  # 绕过缓存，获取最新数据
                word_count_threshold=10,      # 最小词数阈值
                excluded_tags=["script", "style", "nav", "footer"],  # 排除不需要的标签
                exclude_external_links=True,  # 排除外部链接
                exclude_social_media_links=True,  # 排除社交媒体链接
                remove_overlay_elements=True,  # 移除覆盖元素
                wait_for="body",  # 等待页面主体加载完成
                verbose=False  # 减少输出
            )

            async with AsyncWebCrawler() as crawler:
                print(f"正在爬取金十数据: {url}")
                result = await crawler.arun(url=url, config=config)

                if not result.success:
                    print(f"❌ 金十数据爬取失败: {result.error_message}")
                    return []

                content_lines = result.markdown.split('\n')

                # 返回所有非空行
                news_lines = []
                for line in content_lines:
                    if line.strip():  # 只过滤掉空行
                        news_lines.append(line.strip())

                print(f"爬取到金十数据 {len(news_lines)} 行内容")
                print("=" * 80)
                print("爬取到的金十数据内容:")
                print("=" * 80)
                for i, line in enumerate(news_lines, 1):
                    print(f"{i:3d}. {line}")
                print("=" * 80)

                return news_lines
        except Exception as e:
            print(f"爬取金十数据失败: {e}")
            import traceback
            traceback.print_exc()
            return []



    async def analyze_news(self, progress_callback=None, source="cls") -> List[Dict]:
        """完整的新闻分析流程"""
        print(f"🔍 DEBUG: analyze_news 开始，数据源: {source}")
        print(f"🔍 DEBUG: 股票数据状态 - 代码数量: {len(self.stock_codes)}, 名称数量: {len(self.stock_names)}")

        if source == "jin10":
            print("🔍 DEBUG: 调用 analyze_jin10_news")
            return await self.analyze_jin10_news(progress_callback)
        else:
            print("🔍 DEBUG: 调用 analyze_cls_news")
            return await self.analyze_cls_news(progress_callback)

    async def analyze_cls_news(self, progress_callback=None) -> List[Dict]:
        """分析财联社新闻的完整流程"""
        print(f"=== 开始财联社新闻分析流程，爬取真实数据 ===")

        # 1. 历史新闻已在初始化时加载，这里不再重复加载
        print(f"当前历史新闻数量: {len(self.news_history)} 条")

        # 2. 爬取财联社真实数据
        print("正在爬取财联社新闻数据...")
        news_lines = await self.crawl_news()

        if not news_lines:
            print("爬取失败，返回数据库中的历史新闻")
            return []

        # 3. 获取数据库中财联社的最新新闻时间，预过滤旧新闻
        latest_cls_time = self.db.get_latest_cls_news_time()
        if latest_cls_time:
            print(f"🔍 DEBUG: 开始预过滤财联社数据，数据库最新时间: {latest_cls_time}")
            filtered_lines = self.filter_news_lines_by_time(news_lines, latest_cls_time)
            print(f"🔍 DEBUG: 财联社数据预过滤后剩余 {len(filtered_lines)} 行内容")
            news_lines = filtered_lines
        else:
            print("🔍 DEBUG: 数据库中没有财联社记录，处理所有新闻内容")

        # 4. 匹配包含股票名称或代码的新闻
        print("开始匹配股票相关新闻...")
        matched_news = self.match_stock_content(news_lines)
        print(f"匹配到 {len(matched_news)} 条股票相关新闻")

        # 5. 股票验证和情绪分析
        return await self._process_matched_news(matched_news, progress_callback, "财联社")

    async def analyze_jin10_news(self, progress_callback=None) -> List[Dict]:
        """分析金十数据新闻的完整流程"""
        print(f"=== 开始金十数据新闻分析流程，爬取真实数据 ===")

        # 1. 历史新闻已在初始化时加载，这里不再重复加载
        print(f"当前历史新闻数量: {len(self.news_history)} 条")

        # 2. 爬取金十数据真实数据
        print("正在爬取金十数据新闻数据...")
        news_lines = await self.crawl_jin10_news()
        print(f"🔍 DEBUG: 爬取到 {len(news_lines) if news_lines else 0} 行原始数据")

        if not news_lines:
            print("❌ 爬取失败，返回空列表")
            return []

        # 显示前几行原始数据用于调试
        print("🔍 DEBUG: 原始数据前5行:")
        for i, line in enumerate(news_lines[:5]):
            print(f"  {i+1}. {line[:100]}...")
        if len(news_lines) > 5:
            print(f"  ... 还有 {len(news_lines) - 5} 行数据")

        # 3. 获取数据库中金十数据的最新新闻时间，预过滤旧新闻
        latest_jin10_time = self.db.get_latest_jin10_news_time()
        if latest_jin10_time:
            print(f"🔍 DEBUG: 开始预过滤金十数据，数据库最新时间: {latest_jin10_time}")
            filtered_lines = self.filter_jin10_news_lines_by_time(news_lines, latest_jin10_time)
            print(f"🔍 DEBUG: 金十数据预过滤后剩余 {len(filtered_lines)} 行内容")
            news_lines = filtered_lines
        else:
            print("🔍 DEBUG: 数据库中没有金十数据记录，处理所有新闻内容")

        # 4. 匹配包含股票名称或代码的新闻
        print("🔍 DEBUG: 开始匹配金十数据股票相关新闻...")
        print(f"🔍 DEBUG: 当前股票数据 - 代码: {len(self.stock_codes)}, 名称: {len(self.stock_names)}")
        if len(self.stock_names) > 0:
            print(f"🔍 DEBUG: 前3个股票名称: {self.stock_names[:3]}")

        matched_news = self.match_jin10_stock_content(news_lines)
        print(f"🔍 DEBUG: 匹配结果 - 找到 {len(matched_news)} 条股票相关新闻")

        # 显示匹配到的新闻详情
        if matched_news:
            print("🔍 DEBUG: 匹配到的新闻详情:")
            for i, news in enumerate(matched_news[:3]):  # 只显示前3条
                print(f"  {i+1}. 股票: {news['stock']}, 类型: {news.get('stock_type', 'N/A')}")
                print(f"      内容: {news['content'][:80]}...")
        else:
            print("🔍 DEBUG: 未匹配到任何股票相关新闻")

        # 5. 股票验证和情绪分析
        return await self._process_matched_news(matched_news, progress_callback, "金十数据")

    async def _process_matched_news(self, matched_news: List[Dict], progress_callback, source_name: str) -> List[Dict]:
        """处理匹配到的新闻：验证、过滤和情绪分析"""
        print(f"开始{source_name}股票验证和情绪分析...")
        new_analyzed_news = []
        stock_filtered_count = 0  # 非A股/港股通股票过滤数量
        fact_filtered_count = 0   # 股价既定事实过滤数量

        for i, news_item in enumerate(matched_news):
            news_time = news_item.get('news_time', f"未知时间_{i}")

            print(f"处理第 {i+1} 条新闻，提取的时间: {news_time}")
            print(f"处理第 {i+1} 条新闻: {news_item['stock']}")

            # 过滤股价既定事实（股票已通过match_stock_content验证）
            if self.glm_client:
                print(f"进行事实过滤: {news_item['stock']}")
                validation_result = self.verify_stock_validity(news_item['stock'], news_item['content'])

                if validation_result == "股价既定事实":
                    fact_filtered_count += 1
                    print(f"第 {i+1} 条新闻被过滤，属于股价既定涨跌事实: {news_item['stock']}")
                    continue
                elif validation_result == "有效":
                    print(f"事实过滤通过: {news_item['stock']}")
                else:
                    print(f"事实过滤结果未知: {validation_result}，默认通过")
            else:
                print("GLM客户端未初始化，跳过事实过滤")

            # 进行情绪分析
            sentiment = "未分析"
            if self.glm_client:
                print(f"分析市场情绪: {news_item['stock']}")
                sentiment = self.analyze_sentiment(news_item['content'], news_item['stock'])
                print(f"情绪分析结果: {sentiment}")
            else:
                print("GLM客户端未初始化，跳过情绪分析")

            # 转换时间格式为可排序的格式
            sortable_timestamp = self.convert_to_sortable_timestamp(news_time)

            analyzed_item = {
                'timestamp': sortable_timestamp,
                'content': news_item['content'],
                'stock': news_item['stock'],
                'match_type': news_item['match_type'],
                'sentiment': sentiment,
                'news_time': news_time,  # 保留原始时间用于显示
                'display_time': news_time,  # 显示用的时间
                'source': source_name,  # 添加数据源标识
                'stock_type': news_item.get('stock_type', 'A股')  # 添加股票类型
            }

            # 如果是金十数据，添加序号信息
            if 'sequence_num' in news_item:
                analyzed_item['sequence_num'] = news_item['sequence_num']

            # 保存到数据库
            if self.db.save_news(analyzed_item):
                new_analyzed_news.append(analyzed_item)
                # 添加到历史记录
                self.news_history.append(analyzed_item)
                print(f"新闻已保存到数据库: {news_item['stock']}")

                # 如果有回调函数，立即调用以更新UI
                if progress_callback:
                    try:
                        print(f"调用进度回调，更新UI显示新闻: {news_item['stock']}")
                        progress_callback(analyzed_item)
                        # 添加小延迟，让UI有时间更新
                        await asyncio.sleep(0.1)
                    except Exception as e:
                        print(f"回调函数执行出错: {e}")

        # 历史记录已在循环中逐条添加，这里不再批量添加
        total_filtered = stock_filtered_count + fact_filtered_count
        print(f"{source_name}分析完成，新增 {len(new_analyzed_news)} 条新闻")
        print(f"过滤统计: {stock_filtered_count} 条非A股/港股通新闻，{fact_filtered_count} 条股价既定事实新闻")
        print(f"总历史记录: {len(self.news_history)} 条")

        return new_analyzed_news

    def get_news_history(self) -> List[Dict]:
        """获取新闻历史记录"""
        return self.news_history

    def clear_history(self):
        """清空历史记录"""
        self.news_history = []

    def clear_database(self):
        """清空数据库中的所有新闻"""
        return self.db.clear_all_news()

    def get_database_info(self):
        """获取数据库信息"""
        return self.db.get_database_info()

    def get_news_by_stock(self, stock: str):
        """根据股票获取相关新闻"""
        return self.db.get_news_by_stock(stock)

    def get_news_by_sentiment(self, sentiment: str):
        """根据情绪获取新闻"""
        return self.db.get_news_by_sentiment(sentiment)

    def cleanup_old_news(self, days: int = 7):
        """清理指定天数之前的旧新闻"""
        try:
            print(f"=== 启动时清理 {days} 天前的旧新闻 ===")
            success = self.db.delete_old_news(days)
            if success:
                print(f"✅ 旧新闻清理完成")
            else:
                print(f"❌ 旧新闻清理失败")
        except Exception as e:
            print(f"❌ 清理旧新闻时出错: {e}")
