# ///////////////////////////////////////////////////////////////
#
# 新闻数据库模块
# 负责新闻数据的持久化存储和管理
#
# ///////////////////////////////////////////////////////////////

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional

class NewsDatabase:
    def __init__(self, db_path: str = None):
        """初始化数据库连接"""
        if db_path is None:
            # 默认将数据库文件放在 Modern_GUI_PyDracula 目录下
            current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            db_path = os.path.join(current_dir, "news_database.db")

        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库，创建新闻表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建新闻表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS news (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    content TEXT NOT NULL,
                    stock TEXT NOT NULL,
                    match_type TEXT NOT NULL,
                    sentiment TEXT NOT NULL,
                    news_time TEXT NOT NULL,
                    source TEXT DEFAULT '财联社',
                    sequence_num TEXT DEFAULT NULL,
                    stock_type TEXT DEFAULT 'A',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(news_time)
                )
            ''')

            # 创建配置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT NOT NULL,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            print("数据库初始化成功")
            
        except Exception as e:
            print(f"数据库初始化失败: {e}")

    def get_latest_news_time(self) -> Optional[str]:
        """获取数据库中最新一条新闻的时间"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 按timestamp降序排列，获取最新的一条记录
            cursor.execute('''
                SELECT timestamp FROM news
                ORDER BY timestamp DESC
                LIMIT 1
            ''')

            result = cursor.fetchone()
            conn.close()

            if result:
                latest_time = result[0]
                print(f"数据库中最新新闻时间: {latest_time}")
                return latest_time
            else:
                print("数据库中没有新闻记录")
                return None

        except Exception as e:
            print(f"获取最新新闻时间失败: {e}")
            return None

    def get_latest_jin10_news_time(self) -> Optional[str]:
        """获取数据库中最新一条金十数据新闻的时间"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 按timestamp降序排列，获取最新的一条金十数据记录
            cursor.execute('''
                SELECT timestamp FROM news
                WHERE source = '金十数据'
                ORDER BY timestamp DESC
                LIMIT 1
            ''')

            result = cursor.fetchone()
            conn.close()

            if result:
                latest_time = result[0]
                print(f"数据库中最新金十数据新闻时间: {latest_time}")
                return latest_time
            else:
                print("数据库中没有金十数据新闻记录")
                return None

        except Exception as e:
            print(f"获取最新金十数据新闻时间失败: {e}")
            return None

    def get_latest_cls_news_time(self) -> Optional[str]:
        """获取数据库中最新一条财联社新闻的时间"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 按timestamp降序排列，获取最新的一条财联社记录
            cursor.execute('''
                SELECT timestamp FROM news
                WHERE source = '财联社'
                ORDER BY timestamp DESC
                LIMIT 1
            ''')

            result = cursor.fetchone()
            conn.close()

            if result:
                latest_time = result[0]
                print(f"数据库中最新财联社新闻时间: {latest_time}")
                return latest_time
            else:
                print("数据库中没有财联社新闻记录")
                return None

        except Exception as e:
            print(f"获取最新财联社新闻时间失败: {e}")
            return None

    def load_all_news(self) -> List[Dict]:
        """从数据库加载所有历史新闻"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT timestamp, content, stock, match_type, sentiment, news_time, source, sequence_num, stock_type
                FROM news
                ORDER BY created_at DESC
            ''')

            rows = cursor.fetchall()
            conn.close()

            news_list = []
            for row in rows:
                # 将标准时间格式转换为中文显示格式
                display_time = self.convert_timestamp_to_display_format(row[0])

                news_item = {
                    'timestamp': row[0],
                    'content': row[1],
                    'stock': row[2],
                    'match_type': row[3],
                    'sentiment': row[4],
                    'news_time': row[5],
                    'source': row[6] if row[6] else '财联社',  # 兼容旧数据
                    'sequence_num': row[7],
                    'stock_type': row[8] if len(row) > 8 and row[8] else 'A',  # 兼容旧数据
                    'display_time': display_time  # 添加显示用的时间格式
                }
                news_list.append(news_item)

            print(f"从数据库加载了 {len(news_list)} 条历史新闻")
            return news_list

        except Exception as e:
            print(f"从数据库加载新闻失败: {e}")
            return []

    def convert_timestamp_to_display_format(self, timestamp: str) -> str:
        """将标准时间格式转换为中文显示格式"""
        try:
            from datetime import datetime

            # 解析标准时间格式 "2025-07-01 14:30:00"
            dt = datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S")

            # 转换为中文格式 "7月1日 14:30:00"
            display_format = f"{dt.month}月{dt.day}日 {dt.strftime('%H:%M:%S')}"

            return display_format

        except Exception as e:
            print(f"时间格式转换失败: {e}, 原始时间: {timestamp}")
            # 转换失败时返回原始时间
            return timestamp

    def save_news(self, news_item: Dict) -> bool:
        """保存单条新闻到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR IGNORE INTO news
                (timestamp, content, stock, match_type, sentiment, news_time, source, sequence_num, stock_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                news_item['timestamp'],
                news_item['content'],
                news_item['stock'],
                news_item['match_type'],
                news_item['sentiment'],
                news_item.get('news_time', news_item['timestamp']),
                news_item.get('source', '财联社'),
                news_item.get('sequence_num', None),
                news_item.get('stock_type', 'A')
            ))

            conn.commit()
            rows_affected = cursor.rowcount
            conn.close()

            return rows_affected > 0

        except Exception as e:
            print(f"保存新闻到数据库失败: {e}")
            return False

    def save_multiple_news(self, news_list: List[Dict]) -> int:
        """批量保存新闻到数据库"""
        saved_count = 0
        for news_item in news_list:
            if self.save_news(news_item):
                saved_count += 1
        return saved_count

    def is_news_duplicate(self, news_time: str) -> bool:
        """检查新闻是否已存在（仅基于时间判断）"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT COUNT(*) FROM news
                WHERE news_time = ?
            ''', (news_time,))

            count = cursor.fetchone()[0]
            conn.close()

            return count > 0

        except Exception as e:
            print(f"检查新闻重复性失败: {e}")
            return False

    def get_news_count(self) -> int:
        """获取数据库中新闻总数"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM news')
            count = cursor.fetchone()[0]
            conn.close()
            
            return count
            
        except Exception as e:
            print(f"获取新闻总数失败: {e}")
            return 0

    def get_news_by_stock(self, stock: str) -> List[Dict]:
        """根据股票名称获取相关新闻"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT timestamp, content, stock, match_type, sentiment, news_time
                FROM news 
                WHERE stock = ?
                ORDER BY created_at DESC
            ''', (stock,))
            
            rows = cursor.fetchall()
            conn.close()
            
            news_list = []
            for row in rows:
                # 将标准时间格式转换为中文显示格式
                display_time = self.convert_timestamp_to_display_format(row[0])

                news_item = {
                    'timestamp': row[0],
                    'content': row[1],
                    'stock': row[2],
                    'match_type': row[3],
                    'sentiment': row[4],
                    'news_time': row[5],
                    'display_time': display_time  # 添加显示用的时间格式
                }
                news_list.append(news_item)
            
            return news_list
            
        except Exception as e:
            print(f"根据股票获取新闻失败: {e}")
            return []

    def get_news_by_sentiment(self, sentiment: str) -> List[Dict]:
        """根据情绪获取新闻"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT timestamp, content, stock, match_type, sentiment, news_time
                FROM news 
                WHERE sentiment = ?
                ORDER BY created_at DESC
            ''', (sentiment,))
            
            rows = cursor.fetchall()
            conn.close()
            
            news_list = []
            for row in rows:
                # 将标准时间格式转换为中文显示格式
                display_time = self.convert_timestamp_to_display_format(row[0])

                news_item = {
                    'timestamp': row[0],
                    'content': row[1],
                    'stock': row[2],
                    'match_type': row[3],
                    'sentiment': row[4],
                    'news_time': row[5],
                    'display_time': display_time  # 添加显示用的时间格式
                }
                news_list.append(news_item)
            
            return news_list
            
        except Exception as e:
            print(f"根据情绪获取新闻失败: {e}")
            return []

    def clear_all_news(self) -> bool:
        """清空所有新闻数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('DELETE FROM news')
            conn.commit()

            rows_affected = cursor.rowcount
            conn.close()

            print(f"已清空 {rows_affected} 条新闻记录")
            return True

        except Exception as e:
            print(f"清空新闻数据失败: {e}")
            return False

    def delete_old_news(self, days: int = 7) -> bool:
        """删除指定天数之前的新闻"""
        try:
            from datetime import datetime, timedelta

            # 计算截止日期
            cutoff_date = datetime.now() - timedelta(days=days)
            cutoff_timestamp = cutoff_date.strftime("%Y-%m-%d %H:%M:%S")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 删除指定日期之前的新闻
            cursor.execute('DELETE FROM news WHERE timestamp < ?', (cutoff_timestamp,))
            conn.commit()

            rows_affected = cursor.rowcount
            conn.close()

            print(f"已删除 {days} 天前的 {rows_affected} 条新闻记录")
            return True

        except Exception as e:
            print(f"删除旧新闻失败: {e}")
            return False

    def get_database_info(self) -> Dict:
        """获取数据库信息"""
        try:
            info = {
                'db_path': self.db_path,
                'db_exists': os.path.exists(self.db_path),
                'total_news': self.get_news_count(),
                'db_size': os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
            }
            return info
            
        except Exception as e:
            print(f"获取数据库信息失败: {e}")
            return {}

    def save_token(self, token: str) -> bool:
        """保存AI聊天机器人的token"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 使用INSERT OR REPLACE来更新或插入token
            cursor.execute('''
                INSERT OR REPLACE INTO config (key, value, updated_at)
                VALUES (?, ?, ?)
            ''', ('chatbot_token', token, datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

            conn.commit()
            conn.close()

            print(f"Token已保存到数据库")
            return True

        except Exception as e:
            print(f"保存Token失败: {e}")
            return False

    def get_token(self) -> Optional[str]:
        """从数据库获取AI聊天机器人的token"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT value FROM config WHERE key = ?', ('chatbot_token',))
            result = cursor.fetchone()
            conn.close()

            if result:
                token = result[0]
                print(f"从数据库读取到Token: {token[:20]}...")  # 只显示前20个字符
                return token
            else:
                print("数据库中未找到Token")
                return None

        except Exception as e:
            print(f"读取Token失败: {e}")
            return None

    def delete_news_by_timestamp_and_stock(self, timestamp: str, stock: str) -> bool:
        """根据时间戳和股票信息删除特定新闻记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 删除匹配的新闻记录
            cursor.execute(
                'DELETE FROM news WHERE timestamp = ? AND stock = ?',
                (timestamp, stock)
            )

            conn.commit()
            rows_affected = cursor.rowcount
            conn.close()

            if rows_affected > 0:
                print(f"成功删除新闻记录: timestamp={timestamp}, stock={stock}")
                return True
            else:
                print(f"未找到匹配的新闻记录: timestamp={timestamp}, stock={stock}")
                return False

        except Exception as e:
            print(f"删除新闻记录失败: {e}")
            return False

    def close(self):
        """关闭数据库连接（预留方法）"""
        # SQLite3 连接在每次操作后都会关闭，这里预留给其他数据库类型
        pass
