#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
金十数据网站爬虫
参考 Modern_GUI_PyDracula/modules/news_analyzer.py 中的爬取逻辑
爬取 https://www.jin10.com/index.html 的所有数据并打印出来
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import List, Dict, Optional

# 添加 Modern_GUI_PyDracula/modules 到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
modules_path = os.path.join(current_dir, "Modern_GUI_PyDracula", "modules")
if modules_path not in sys.path:
    sys.path.insert(0, modules_path)

try:
    from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode
    print("✅ crawl4ai 模块导入成功")
except ImportError as e:
    print(f"❌ crawl4ai 模块导入失败: {e}")
    print("请确保已安装 crawl4ai 库")
    sys.exit(1)


class Jin10Crawler:
    """金十数据网站爬虫类"""
    
    def __init__(self):
        self.target_url = "https://www.jin10.com/index.html"
        print(f"🎯 目标网站: {self.target_url}")
    
    async def crawl_jin10_data(self) -> List[str]:
        """
        爬取金十数据网站内容
        参考 news_analyzer.py 中的 crawl_news 方法
        """
        print(f"🚀 开始爬取金十数据网站...")
        print(f"📅 爬取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        try:
            # 创建爬取配置
            config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,  # 绕过缓存，获取最新数据
                word_count_threshold=10,      # 最小词数阈值
                excluded_tags=["script", "style", "nav", "footer"],  # 排除不需要的标签
                exclude_external_links=True,  # 排除外部链接
                exclude_social_media_links=True,  # 排除社交媒体链接
                remove_overlay_elements=True,  # 移除覆盖元素
                wait_for="body",  # 等待页面主体加载完成
                verbose=True  # 详细输出
            )
            
            # 使用 AsyncWebCrawler 进行爬取
            async with AsyncWebCrawler() as crawler:
                print("🔄 正在连接金十数据网站...")
                result = await crawler.arun(url=self.target_url, config=config)
                
                if not result.success:
                    print(f"❌ 爬取失败: {result.error_message}")
                    return []
                
                print(f"✅ 爬取成功!")
                print(f"📊 状态码: {result.status_code}")
                print(f"📄 HTML 长度: {len(result.html)} 字符")
                print(f"🧹 清理后 HTML 长度: {len(result.cleaned_html or '')} 字符")
                print(f"📝 Markdown 长度: {len(result.markdown)} 字符")
                
                # 将 markdown 内容按行分割
                content_lines = result.markdown.split('\n')
                
                # 过滤空行
                news_lines = []
                for line in content_lines:
                    if line.strip():  # 只保留非空行
                        news_lines.append(line.strip())
                
                print(f"📋 提取到 {len(news_lines)} 行有效内容")
                print("=" * 80)
                
                return news_lines
                
        except Exception as e:
            print(f"❌ 爬取过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def print_crawled_data(self, content_lines: List[str]):
        """
        打印爬取到的数据
        """
        if not content_lines:
            print("❌ 没有爬取到任何数据")
            return
        
        print("📋 爬取到的金十数据内容:")
        print("=" * 80)
        
        for i, line in enumerate(content_lines, 1):
            # 限制每行显示长度，避免输出过长
            display_line = line[:200] + "..." if len(line) > 200 else line
            print(f"{i:4d}. {display_line}")
        
        print("=" * 80)
        print(f"📊 总计: {len(content_lines)} 行数据")
        
        # 统计信息
        self.print_statistics(content_lines)
    
    def print_statistics(self, content_lines: List[str]):
        """打印统计信息"""
        print("\n📈 数据统计:")
        print("-" * 40)
        
        # 基本统计
        total_lines = len(content_lines)
        total_chars = sum(len(line) for line in content_lines)
        avg_line_length = total_chars / total_lines if total_lines > 0 else 0
        
        print(f"总行数: {total_lines}")
        print(f"总字符数: {total_chars}")
        print(f"平均行长度: {avg_line_length:.1f} 字符")
        
        # 内容类型分析
        headers = [line for line in content_lines if line.startswith('#')]
        links = [line for line in content_lines if 'http' in line.lower()]
        chinese_lines = [line for line in content_lines if any('\u4e00' <= char <= '\u9fff' for char in line)]
        
        print(f"标题行数: {len(headers)}")
        print(f"包含链接的行数: {len(links)}")
        print(f"包含中文的行数: {len(chinese_lines)}")
        
        # 长度分布
        short_lines = [line for line in content_lines if len(line) < 50]
        medium_lines = [line for line in content_lines if 50 <= len(line) < 200]
        long_lines = [line for line in content_lines if len(line) >= 200]
        
        print(f"短行 (<50字符): {len(short_lines)}")
        print(f"中等行 (50-200字符): {len(medium_lines)}")
        print(f"长行 (>=200字符): {len(long_lines)}")
    
    async def run(self):
        """运行爬虫"""
        print("🎉 金十数据网站爬虫启动")
        print("=" * 80)
        
        # 爬取数据
        content_lines = await self.crawl_jin10_data()
        
        # 打印结果
        self.print_crawled_data(content_lines)
        
        print("\n🎉 爬取完成!")


async def main():
    """主函数"""
    crawler = Jin10Crawler()
    await crawler.run()


if __name__ == "__main__":
    # 运行爬虫
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
